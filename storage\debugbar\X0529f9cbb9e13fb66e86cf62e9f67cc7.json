{"__meta": {"id": "X0529f9cbb9e13fb66e86cf62e9f67cc7", "datetime": "2025-06-21 17:42:25", "utime": 1750527745.152165, "method": "GET", "uri": "/backend/default-team/tasks", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750527743.969098, "end": 1750527745.152187, "duration": 1.183089017868042, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1750527743.969098, "relative_start": 0, "end": **********.292114, "relative_end": **********.292114, "duration": 0.3230159282684326, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.292122, "relative_start": 0.32302379608154297, "end": 1750527745.152189, "relative_end": 1.9073486328125e-06, "duration": 0.8600671291351318, "duration_str": "860ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET backend/{tenant}/tasks", "domain": null, "middleware": "panel:backend, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\HandleSuperAdminAccess, Filament\\Http\\Middleware\\IdentifyTenant, App\\Http\\Middleware\\EnforceTenantAccess, BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant, App\\Http\\Middleware\\RestrictTenantPermissions", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable@__invoke", "as": "filament.backend.resources.tasks.index", "namespace": null, "prefix": "backend/{tenant:slug}/tasks", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.012000000000000004, "accumulated_duration_str": "12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.678366, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 4.333}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 2 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.685165, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "User.php:187", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=187", "ajax": false, "filename": "User.php", "line": "187"}, "connection": "edu_db2", "explain": null, "start_percent": 4.333, "width_percent": 3}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.687883, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 7.333, "width_percent": 1.917}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.714577, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 9.25, "width_percent": 6.25}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.7181358, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 15.5, "width_percent": 2.167}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.720079, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 17.667, "width_percent": 1.833}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.722217, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 19.5, "width_percent": 3}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.724499, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 22.5, "width_percent": 4}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.726944, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 26.5, "width_percent": 2.167}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.729138, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 28.667, "width_percent": 1.833}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.731231, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 30.5, "width_percent": 2.417}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7384238, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 32.917, "width_percent": 3.5}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.740583, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 36.417, "width_percent": 2}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.742275, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 38.417, "width_percent": 1.75}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.744192, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 40.167, "width_percent": 3.417}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.746328, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 43.583, "width_percent": 2.25}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7480628, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 45.833, "width_percent": 1.833}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.74976, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 47.667, "width_percent": 1.5}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.751425, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 49.167, "width_percent": 2.25}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.030652, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 51.417, "width_percent": 2.583}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": ["new", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.0341802, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 54, "width_percent": 5.417}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.038288, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 59.417, "width_percent": 2.583}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasRoutes.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 199}], "start": 1750527745.057515, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 62, "width_percent": 3.25}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.06134, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 65.25, "width_percent": 1.5}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.06325, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 66.75, "width_percent": 2.5}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.065864, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 69.25, "width_percent": 1.25}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": "view", "name": "filament-panels::components.tenant-menu", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/vendor/filament-panels/components/tenant-menu.blade.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750527745.07095, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 70.5, "width_percent": 3.167}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.075964, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 73.667, "width_percent": 1.583}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.0779321, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 75.25, "width_percent": 2.833}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.080741, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 78.083, "width_percent": 1.5}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.084755, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 79.583, "width_percent": 2.167}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.086773, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 81.75, "width_percent": 2.083}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.089129, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 83.833, "width_percent": 1.25}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.093631, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 85.083, "width_percent": 1.667}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.0956829, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 86.75, "width_percent": 2}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.0981948, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 88.75, "width_percent": 1.917}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.102574, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 90.667, "width_percent": 2.333}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.104969, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 93, "width_percent": 4.333}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750527745.109369, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 97.333, "width_percent": 2.667}]}, "models": {"data": {"App\\Models\\TeachingSchedule": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeachingSchedule.php&line=1", "ajax": false, "filename": "TeachingSchedule.php", "line": "?"}}, "App\\Models\\Task": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTask.php&line=1", "ajax": false, "filename": "Task.php", "line": "?"}}, "App\\Models\\Lesson": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FLesson.php&line=1", "ajax": false, "filename": "Lesson.php", "line": "?"}}, "App\\Models\\User": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ClassRoom": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FClassRoom.php&line=1", "ajax": false, "filename": "ClassRoom.php", "line": "?"}}, "App\\Models\\Subject": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FSubject.php&line=1", "ajax": false, "filename": "Subject.php", "line": "?"}}, "App\\Models\\Team": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 139, "is_counter": true}, "livewire": {"data": {"app.filament.resources.task-resource.pages.task-timetable #ySTXiBqQtmiF3JKKHGb0": "array:4 [\n  \"data\" => array:20 [\n    \"selectedDate\" => \"2025-06-21\"\n    \"viewMode\" => \"week\"\n    \"editingTask\" => null\n    \"editingSchedule\" => null\n    \"editTaskData\" => []\n    \"editScheduleData\" => []\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.resources.task-resource.pages.task-timetable\"\n  \"component\" => \"App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable\"\n  \"id\" => \"ySTXiBqQtmiF3JKKHGb0\"\n]", "filament.livewire.global-search #pdDoQXeIlAhyE8RIkUCk": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"pdDoQXeIlAhyE8RIkUCk\"\n]", "filament.livewire.notifications #t2jjSF7Etv3eI5o70uPm": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2913\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"t2jjSF7Etv3eI5o70uPm\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 43, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1408333885 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408333885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695741, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-173653972 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-173653972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.028115, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1070251190 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070251190\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.028563, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-430933803 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430933803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.028986, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1097335485 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097335485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.037163, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-934093556 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934093556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.037515, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1407637679 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407637679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.037951, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-815627683 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815627683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.040282, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1532244485 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532244485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.060407, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2145037506 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145037506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.060626, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-336881277 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-336881277\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.060835, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2058325887 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058325887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.065178, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1339639644 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339639644\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.065425, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1031422366 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031422366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.065634, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1814526383 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814526383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.067376, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2037916409 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037916409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.075026, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1566260150 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566260150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.075244, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2086135570 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086135570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.075446, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-348457616 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348457616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.080038, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-323226817 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323226817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.080289, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1567674824 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567674824\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.080501, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-762802320 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762802320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.082394, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1206821704 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206821704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.083603, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-295313053 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295313053\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.083863, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-291594198 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291594198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.084168, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-174096188 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174096188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.088464, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2133394596 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133394596\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.088704, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1874968102 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874968102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.08891, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-317914677 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317914677\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.090611, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1535525624 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1535525624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.092723, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2081065077 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081065077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.09293, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1679291720 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679291720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.093128, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1421148259 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421148259\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.097498, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-958183278 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958183278\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.097735, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1590181909 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590181909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.097953, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1662131748 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662131748\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.100357, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-437604750 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437604750\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.101628, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-67468092 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67468092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.101835, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1219239053 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219239053\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.102037, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-701323283 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701323283\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.108254, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1182745929 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182745929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.108661, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-204035505 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-204035505\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.108909, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-420502330 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420502330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750527745.111484, "xdebug_link": null}]}, "session": {"_token": "HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "password_hash_web": "$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/default-team/tasks\"\n]"}, "request": {"path_info": "/backend/default-team/tasks", "status_code": "<pre class=sf-dump id=sf-dump-610481325 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-610481325\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1001227296 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1001227296\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-108522102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-108522102\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1197940867 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlhMVXJOM3UxdElLRmdvZEo0QWg1WVE9PSIsInZhbHVlIjoiNzVhV3pab29VMDZVRWducTY1aHQzbDV5UzZLQ2hWd1ZmWldtWjFlSWY0V0hkNEpuYTZjSVhVL0pCYmNUTngvZmRCb0pUOTZlM0thT1VScWdCbUxydU9oeUlWbnpVTS8yeHd4U2VzWVhOclczYUE4WEhMWGdCRHFqVEkxVWlkVklsdE9JL2t1bnRVQjlXMHF2N2pzTlRJbi9JVGJQdk42U1hBU0xIcnUzZVN1TnhDL1pmU01mTUsyZndTOGtMYjJpWG5qNkY4WnNkUFhuRDlEejRrL0l5QjNWZEpLc3lFdXA3Sm5hVEJPWHFRWT0iLCJtYWMiOiI1YWU3ODA0MTJjNGZhOTI5NzE0YTFkNmI3NzA2MDYwZWY4ZmUxMjI3ZDMwYjA1MDA3MWU5Y2NlMGI5OTFmMzRmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InUrVXo3WDJaellFOWRLeUVMUi9sM2c9PSIsInZhbHVlIjoic05lVmtaNlhCaTRuS1U4NlE4VmJoN1RWcW1oNVBMcTNlWkR5WHU2QXU4emNOUmhOUSt2OG1wSTFnR2tGc1o5TW03K3V2NXdWeTlxTzZZaGJPRU1Bd2VjU1d3VTJwZXM1cUh5VDZVTWZhVkVGQzZDMkxVYzl1cm40Uno0YngycFEiLCJtYWMiOiI0Y2E2YjljNGMxNGU3ZjQyODY1YmNkZTljYTJmYmYyY2NkZDZhNTU0MmNlOTI1NjEwNTE5NDAzYWE3MDk1Yzg4IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IjVxQ2tHcHExRVNreHNuTDRwREdrY2c9PSIsInZhbHVlIjoiclBNU1lBdXNVZ3daenRBWTVXM1hNY0o3L0RuQ1lGRU01U2hmQjlpaXpwaTR5N3R5bXRtVEV4bGxKY093QnM1dVZjRVQ3UE0yTFAzNFNneVdPQktmSmJyUXBFbWRWMXRwdlI1ZzN2YVI3ZmJyYVp6QmVmWk9DK0xucDdLZEk0WlAiLCJtYWMiOiIyM2E3OThmMGEwMDBiMmNiZDRlNGRkM2E4M2U3YWZiNzQ4ODQ2OWMxNWNjNWEzNTExZjM2Mzk2NzlmYzg2ZDRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197940867\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2041475153 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|ehyf0vWTi0nhH6BCaRuiRLr53ca7SdmqrBrbPg7dO3Vyvr6mhT3JSPnhkcqE|$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vsL2QWIgJub4i1B2dsBCWSF9PlW0AmpHCXRUUDpe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041475153\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1833039527 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:42:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjlVSzVZeGJKOWlEdjVrN0l2L1ZyVUE9PSIsInZhbHVlIjoid2xlNDhlazlTSVVGWlZQZDNBSE9FT3A2dno5RXlSdXU3NGROcEpRUzJ0dlc4SXk5ZE1xYVdseXpydmhoT1R6QUkyeURRQ3ZHTWRPNlBDODcwY0x2UFdiQ1kvblNFeitONFU2RGRJSFExSTk3eTlUWVJIWDZLSEY0dVZYUDF3dkciLCJtYWMiOiJmYjJmMWFhM2I3N2ZlZDFmNWVkMzk2MjAwY2RhYWU5YzQxNGJjYTZkMTg3YzAxYmYzNzc4NGM2ZGZiNGY2NGMwIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:42:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IjZham1KM3c2ZHJ2dXIzUURPWWtoQUE9PSIsInZhbHVlIjoiek9tYTBEenN3WWViM0JxRWQ5QXVKcWNPZ01MM2VXWHN4WGt3eGd3cWs0bFlCbThRYkllb0dmdjhHdlFFdHV2U0dVSG5sa2lqeHZNWkRyUWNIUjdqTE81M25hTGF4eWFLSFl1U05Sdi9waW9IWTZuMWNyOTdyNWQvb1pPYjBWOFIiLCJtYWMiOiJjMGEwM2YyN2UxOWNjYTUxNGU1ZTRiNjVkM2FkYmM3N2Y1YjBjOTBhYjc1NzM4NjBmMTdlOGM4ZWNlMDVjMzMxIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:42:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjlVSzVZeGJKOWlEdjVrN0l2L1ZyVUE9PSIsInZhbHVlIjoid2xlNDhlazlTSVVGWlZQZDNBSE9FT3A2dno5RXlSdXU3NGROcEpRUzJ0dlc4SXk5ZE1xYVdseXpydmhoT1R6QUkyeURRQ3ZHTWRPNlBDODcwY0x2UFdiQ1kvblNFeitONFU2RGRJSFExSTk3eTlUWVJIWDZLSEY0dVZYUDF3dkciLCJtYWMiOiJmYjJmMWFhM2I3N2ZlZDFmNWVkMzk2MjAwY2RhYWU5YzQxNGJjYTZkMTg3YzAxYmYzNzc4NGM2ZGZiNGY2NGMwIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:42:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IjZham1KM3c2ZHJ2dXIzUURPWWtoQUE9PSIsInZhbHVlIjoiek9tYTBEenN3WWViM0JxRWQ5QXVKcWNPZ01MM2VXWHN4WGt3eGd3cWs0bFlCbThRYkllb0dmdjhHdlFFdHV2U0dVSG5sa2lqeHZNWkRyUWNIUjdqTE81M25hTGF4eWFLSFl1U05Sdi9waW9IWTZuMWNyOTdyNWQvb1pPYjBWOFIiLCJtYWMiOiJjMGEwM2YyN2UxOWNjYTUxNGU1ZTRiNjVkM2FkYmM3N2Y1YjBjOTBhYjc1NzM4NjBmMTdlOGM4ZWNlMDVjMzMxIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:42:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833039527\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1562590050 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562590050\", {\"maxDepth\":0})</script>\n"}}
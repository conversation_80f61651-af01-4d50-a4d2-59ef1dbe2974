{"__meta": {"id": "Xf309d6e4d2c51ba092fd0614c0a58453", "datetime": "2025-06-22 03:55:01", "utime": 1750564501.009424, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.189948, "end": 1750564501.009448, "duration": 0.8194999694824219, "duration_str": "819ms", "measures": [{"label": "Booting", "start": **********.189948, "relative_start": 0, "end": **********.521767, "relative_end": **********.521767, "duration": 0.3318188190460205, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.521774, "relative_start": 0.33182597160339355, "end": 1750564501.00945, "relative_end": 1.9073486328125e-06, "duration": 0.48767590522766113, "duration_str": "488ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50803400, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0017100000000000001, "accumulated_duration_str": "1.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.922945, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 23.392}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.925942, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 23.392, "width_percent": 13.45}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 308}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}], "start": **********.930417, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "User.php:187", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=187", "ajax": false, "filename": "User.php", "line": "187"}, "connection": "edu_db2", "explain": null, "start_percent": 36.842, "width_percent": 18.713}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.943181, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:75", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=75", "ajax": false, "filename": "DatabaseNotifications.php", "line": "75"}, "connection": "edu_db2", "explain": null, "start_percent": 55.556, "width_percent": 28.07}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.945807, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:97", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=97", "ajax": false, "filename": "DatabaseNotifications.php", "line": "97"}, "connection": "edu_db2", "explain": null, "start_percent": 83.626, "width_percent": 16.374}]}, "models": {"data": {"Illuminate\\Notifications\\DatabaseNotification": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Team": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": {"filament.livewire.database-notifications #utI4Y6q3czj26LUNhsXX": "array:4 [\n  \"data\" => array:1 [\n    \"paginators\" => array:1 [\n      \"database-notifications-page\" => 1\n    ]\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"utI4Y6q3czj26LUNhsXX\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/backend/default-team/tasks\"\n]", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-155322091 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-155322091\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-902801317 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"354 characters\">{&quot;data&quot;:{&quot;paginators&quot;:[{&quot;database-notifications-page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;utI4Y6q3czj26LUNhsXX&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;backend\\/default-team\\/tasks&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;7e07f81478335028b1b41a3709a26de47b60306c34fb3282ef6e14fbfc10b81f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902801317\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-625970654 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">508</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1311 characters\">appearance=system; __next_hmr_refresh_hash__=333; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjF6REVKTlB6UVRDWlVQa1lGVHBWaWc9PSIsInZhbHVlIjoiNEoreDBGNjVwU3JpQTRPYzNwdG9vOFNrSGR6TmZDM0FBTVVjWXJ5c2dYY2xXdTJzOEdyWm0zK1ZJL3llL0xJNGxNRXU0RlFaRDZYSEszaU5IMlMwVFBaK1VnYWNqYUJyMSsrU3U2QUlPc2VySndjeHpTeDRabGFpaERralEwM3pZUkZ3TE1JUHYyejlpdFpaZTk5Wk5ZNVF0cEpJVDljN2daWm8wZ0hIaHphL053WS9Ndm9PK3l0K09wVDQxTStGV2JOeC9SMC9QM3d4MTAzS25VYmszZjJSUjhXWEVjNzVLc3FyNFFZRDVXbz0iLCJtYWMiOiIyYjgyOWMxMzVjYmM2MjRiZjk5MmUyYWM2Yjc4ZTUxMDkxZDkzODQzZDM5ZDMyY2RiNDlkMmVmMWFjMzdkMTMxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlFCeTlnUHl0VFp5VHpWZ0F6NithUkE9PSIsInZhbHVlIjoiQmRCTVo5eXZkd2UwSWk1RGJNN0E4NC9GN3pjSE93L3RSdTMvMTdienczdkhsQWIxb0VReDhvK1Ivbk5uRzlXMG51M0FzUzI5djFtVG9JcVZOUmhLb1Z0dk9zSm8xUHM3WllBNmhZMkJES0xvYTlLUS9QQnVnMmdtTFYyTEQxdTMiLCJtYWMiOiIxZTA0YjAzZDhmNjU1OGQwYzdkODQxNjIyOTkxOWM0ZTkyNTBhNWQ3OTA4ODAxMjhlYzViMDQzY2IzMjE3ZTMxIiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IlBVSFh0OTVVNzQwbFplQTlkSmpac1E9PSIsInZhbHVlIjoiVTFHYjB4K3dLaTRrekYrbnNLbExUWFVmRGV2bVkrLzM0NU1EWGQ2dms4ZGNzMXhZS0RWVzVRRmRWV1E0SVFkR0tTZnFPUjJqUDBqY01DS3I5blVWZEVLRzVlY2dhN0VpMm44SEpQc2dWRGhsT0NtTnpwYVd5Vm9hbEdZM0R5QzciLCJtYWMiOiI3ZWIwNWE4ODYyZWVkNDEwZDE3ODgyNmVjYTA1MjFhOTQxZmY3NGI4MDdhMTE3NWEwYWZmZjFhMzAyMTVhODM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-625970654\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-113374685 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|auVELv1y9Y5lanlgXrSzl83b2PJDR2un8lZsqSpPB8SBoxnjyQzLhkluxh9g|$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8giMLlCM1a53blApVUs4YhDapzkhLDegMd42STu6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113374685\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-86946602 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 03:55:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJVU3dFem03SFovNGI2S3N1aVl5VVE9PSIsInZhbHVlIjoiWG1rdzl6Wkg3S3hhU3FsclF1QnVEbm83L0d2TVJ5Um1uaXlTOG16dW43WFhuUlBOdzNiOGRUeHdFcEdoellwSS9XWUxKT1JFK2NYUitEaGRLYTRDb0Zpc3Q1NmlFWllheVdvZDBmcXdXcmZ2cmxxK1FrSWUvTUVqMURGcXg1WXkiLCJtYWMiOiI1NTJiM2NkMGJiZDlmZWQzYjhiYmJmZjc5M2FkYTFkMzMwYjNkYzVjNTU2YTA4NzE0OTllNDJkYzUyZGU4YzZmIiwidGFnIjoiIn0%3D; expires=Sun, 22 Jun 2025 05:55:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IjlpRVBVS0daTDN4NngvTDBBU0ZMcFE9PSIsInZhbHVlIjoiRjZpK1F0Z1RXRW9iY1F5VXQwSkdnWUsrankzK3M0R1YwOUNObkdjVEpEQkNFSTYwS1ZoNDVnTmVKNmozVDB5ZEU2eFNUcS9yMm90OFVwdncxMndpZUI5VldQdXlNcElLOEE1YVhqamo2Y0U0RHNuZnhNRFBiYTZCQW1UdVlmNmoiLCJtYWMiOiJhMjZmOGY3OGU0YWRiZmQ1NDdjNTMyYWU2ZDZhOTMwN2ZlYzM0YmY5NWUxMzdlOTgwNTdiYmM1MzkwY2EyMzM4IiwidGFnIjoiIn0%3D; expires=Sun, 22 Jun 2025 05:55:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJVU3dFem03SFovNGI2S3N1aVl5VVE9PSIsInZhbHVlIjoiWG1rdzl6Wkg3S3hhU3FsclF1QnVEbm83L0d2TVJ5Um1uaXlTOG16dW43WFhuUlBOdzNiOGRUeHdFcEdoellwSS9XWUxKT1JFK2NYUitEaGRLYTRDb0Zpc3Q1NmlFWllheVdvZDBmcXdXcmZ2cmxxK1FrSWUvTUVqMURGcXg1WXkiLCJtYWMiOiI1NTJiM2NkMGJiZDlmZWQzYjhiYmJmZjc5M2FkYTFkMzMwYjNkYzVjNTU2YTA4NzE0OTllNDJkYzUyZGU4YzZmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 05:55:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IjlpRVBVS0daTDN4NngvTDBBU0ZMcFE9PSIsInZhbHVlIjoiRjZpK1F0Z1RXRW9iY1F5VXQwSkdnWUsrankzK3M0R1YwOUNObkdjVEpEQkNFSTYwS1ZoNDVnTmVKNmozVDB5ZEU2eFNUcS9yMm90OFVwdncxMndpZUI5VldQdXlNcElLOEE1YVhqamo2Y0U0RHNuZnhNRFBiYTZCQW1UdVlmNmoiLCJtYWMiOiJhMjZmOGY3OGU0YWRiZmQ1NDdjNTMyYWU2ZDZhOTMwN2ZlYzM0YmY5NWUxMzdlOTgwNTdiYmM1MzkwY2EyMzM4IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 05:55:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86946602\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-574941328 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574941328\", {\"maxDepth\":0})</script>\n"}}
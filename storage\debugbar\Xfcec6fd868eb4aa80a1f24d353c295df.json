{"__meta": {"id": "Xfcec6fd868eb4aa80a1f24d353c295df", "datetime": "2025-06-21 17:42:49", "utime": **********.484447, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750527768.668136, "end": **********.48447, "duration": 0.8163340091705322, "duration_str": "816ms", "measures": [{"label": "Booting", "start": 1750527768.668136, "relative_start": 0, "end": **********.02961, "relative_end": **********.02961, "duration": 0.36147403717041016, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.02962, "relative_start": 0.3614840507507324, "end": **********.484472, "relative_end": 2.1457672119140625e-06, "duration": 0.4548521041870117, "duration_str": "455ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48618272, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00731, "accumulated_duration_str": "7.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.432446, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 5.472}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.435256, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 5.472, "width_percent": 2.873}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.442166, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 8.345, "width_percent": 3.83}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.44783, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 12.175, "width_percent": 9.576}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.450372, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 21.751, "width_percent": 7.114}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.4526389, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 28.865, "width_percent": 7.798}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.454812, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 36.662, "width_percent": 7.114}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.456903, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 43.776, "width_percent": 6.84}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.459105, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 50.616, "width_percent": 7.387}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.461397, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 58.003, "width_percent": 6.977}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.463499, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 64.979, "width_percent": 6.566}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.4655828, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 71.546, "width_percent": 7.661}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.467763, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 79.207, "width_percent": 6.703}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.469806, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 85.91, "width_percent": 6.703}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.471829, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 92.613, "width_percent": 7.387}]}, "models": {"data": {"App\\Models\\Team": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.revenue-chart #apgYYCehyByJ0Wtl1UGl": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"97ab313d40eee230c600543f6d085ec0\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueChart\"\n  \"id\" => \"apgYYCehyByJ0Wtl1UGl\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/default-team\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "8", "password_hash_web": "$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe", "Dashboard_filters": "array:3 [\n  \"businessCustomersOnly\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1790460434 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1790460434\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1228804317 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1228804317\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2099978744 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"353 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:null,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;apgYYCehyByJ0Wtl1UGl&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;90424167f8e2a999a724e82bc873db67ec794b39d7f3870f13efa840caa1525f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"364 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJidXNpbmVzc0N1c3RvbWVyc09ubHkiOm51bGwsInN0YXJ0RGF0ZSI6bnVsbCwiZW5kRGF0ZSI6bnVsbH0seyJzIjoiYXJyIn1dfSx7InMiOiJhcnIifV19LCJtZW1vIjp7ImlkIjoiV3ZVY3R1T1BNT2dScnAyQmVSWEgiLCJuYW1lIjoiX19tb3VudFBhcmFtc0NvbnRhaW5lciJ9LCJjaGVja3N1bSI6ImRiYjVlYjM2YmIyMmUzNGNkMmNlMGRmOTIxNTg4NjY5ZTUwOWZhNWFlMGNhNWNlYmVjNzRjZmMyZjRiNDI5ZjEifQ==</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099978744\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1258715819 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">917</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,th;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imswb29jOWJiVTRUMk1DbHc2dzlkaHc9PSIsInZhbHVlIjoieEs1blJFUWJRMnhWaEVLcmVKdnRRQnNZcGlqdkx1dGNnMlVVSk9DSjlUalp6M1c5NFhSSWpLVS9ZcFE3VXhEdUhVQ3hkazByZXUzbmQ2bUdDZXorMUZ5aWlJTVp1NjhQaFliMU5NbmVsVC9WdENZRHU0NlpkT1VUaDRQY3IwaG5udnoxOTI4Y0xxc3NCTkRJbkQ5RWxrMGlSK2k0cjJ2dmMvWWZ1bDBWZC9OMzRQdU9CcEUxdVFnQ3ZNcTczK0RTZzBzT2lWaWduMHFUclQ5Wk1VS2UvdEVjL2NhRFIyRzRkN0o1Rmd2VzRKTT0iLCJtYWMiOiI5YjVmODhmOWU5YzA4M2E0YjMxNWE0NTlmNmNmZDg0YzZjMThmZDE5NzliOTA5NjI0Mjg2ODA0YmYwYTkwZWIzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlU3QUJHQm9Cc3E0TCtieFdTQ0FPRmc9PSIsInZhbHVlIjoiQjdsSUR5bTdxUlV0Y3hCZWFFTk0xQTFEVEpQSlN1a1NGaDlSUWlBM3A3NGlGSU0yK3RXQVpjbXRzcXhXY3lKOENBejZoZUR5QTdKUnpnNkRVQUZhRFh3UHhXVmQ0RHYxQVZTWHBIR0dsa3hQTVFoSTk2a1FDODh6TjJHcVVaMXIiLCJtYWMiOiJkZGFkNmMxOGNiNzRjNzk4MWMxMmM0M2EzOWNkMTY2MGNlNWZjYmQzYzdmM2M1NjVkZmMwNTgzNjhiOTY0ZmM2IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IkVzMkdUWXBOMVlST1pHZUtPTmh5aFE9PSIsInZhbHVlIjoiems4S3NGU00wRXBzNWVwZFAwSFpuWmRucjdtRzBzUkU4MDVRY1lPSWhSTjVEMlY5N2EzYWU1RVlWZmdoWnY2TjZLWXVESmtiMXJkdlJHSzBnN1pxUEQ1TG03dkdXVnE1U1BPelBvN3J6djRrb0M5dkRnQjNVTUhIeDFPY3pSOVgiLCJtYWMiOiJkOTNjZmZlMGQ4NWEwZjI2MjRlMWYzZTY4OWNlZGVhMzRiNjMzYTdhYzYyNWIyNTFjNDU4MjdlNjExMTgyMjJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258715819\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-862202836 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">8|uEm0mlZkc74gIicIsEcLEvV0115i1N3Y0ZTpeVcevaERH48GCO4oF8pIiIWH|$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bDakuFXZEjwkzwceMwuwBZu7yieBSY9wGZVonsnp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862202836\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-944960882 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:42:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImY0WksrL3FmZFdVVWtNMnZkRGpPK0E9PSIsInZhbHVlIjoid0tNaTZ3MFlYUzA2WWhMTVFxNXlpRjM4a3pMRzlTdXh3WklLaEFhdi9PUU1oeGxwMGwxRmlwVjRaRVhuSllENUJCdWltelNhSjJSa1U3elp5OFY2V3VETWJHdVcwcUZvbjBUd0RPQ0JFUlRKL2hRcVBHU2tBQmUvOVFnbWVPT1MiLCJtYWMiOiI5NzY2OWQ3ZTAwYzcxYTI1NTUzYmM5MDdiNGM2Njg1ZmE0NzE5MmZjMDE0NjMzZmNlMTg0OGU2MmU3NDNhNDlmIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:42:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IjRiMDdOc3ZkdVB2MEIxWi9uOGErZEE9PSIsInZhbHVlIjoieFU4MUM0L1dvVjdNcU1WNlZUR2VzWGlMNTdmVlZJVHFzOUtlcndrMEdnSm1xWkxjU2JSa2RDN3U1U0E2c0JHaEdPMHFpZXpEaFUrcHJVYUhXMnRrQSt2MTR1V3F6eTFDMjJDeFRDTFA1eDhoVVR6a2NxcmRzelFFTzNvNXlhT04iLCJtYWMiOiI1NDQzODc4NGFhMmI4MWQ0M2I1YjRmMTVjMjRjZmEyYjM2NzI4OTdjMWQ3MDFmNTQwMGE1OWY4NzJjMzMyODhiIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:42:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImY0WksrL3FmZFdVVWtNMnZkRGpPK0E9PSIsInZhbHVlIjoid0tNaTZ3MFlYUzA2WWhMTVFxNXlpRjM4a3pMRzlTdXh3WklLaEFhdi9PUU1oeGxwMGwxRmlwVjRaRVhuSllENUJCdWltelNhSjJSa1U3elp5OFY2V3VETWJHdVcwcUZvbjBUd0RPQ0JFUlRKL2hRcVBHU2tBQmUvOVFnbWVPT1MiLCJtYWMiOiI5NzY2OWQ3ZTAwYzcxYTI1NTUzYmM5MDdiNGM2Njg1ZmE0NzE5MmZjMDE0NjMzZmNlMTg0OGU2MmU3NDNhNDlmIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:42:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IjRiMDdOc3ZkdVB2MEIxWi9uOGErZEE9PSIsInZhbHVlIjoieFU4MUM0L1dvVjdNcU1WNlZUR2VzWGlMNTdmVlZJVHFzOUtlcndrMEdnSm1xWkxjU2JSa2RDN3U1U0E2c0JHaEdPMHFpZXpEaFUrcHJVYUhXMnRrQSt2MTR1V3F6eTFDMjJDeFRDTFA1eDhoVVR6a2NxcmRzelFFTzNvNXlhT04iLCJtYWMiOiI1NDQzODc4NGFhMmI4MWQ0M2I1YjRmMTVjMjRjZmEyYjM2NzI4OTdjMWQ3MDFmNTQwMGE1OWY4NzJjMzMyODhiIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:42:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944960882\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-109380175 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>Dashboard_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>businessCustomersOnly</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109380175\", {\"maxDepth\":0})</script>\n"}}
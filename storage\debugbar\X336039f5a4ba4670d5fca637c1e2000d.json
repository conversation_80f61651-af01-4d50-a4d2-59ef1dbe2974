{"__meta": {"id": "X336039f5a4ba4670d5fca637c1e2000d", "datetime": "2025-06-21 17:51:29", "utime": **********.415834, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750528287.588426, "end": **********.415879, "duration": 1.8274528980255127, "duration_str": "1.83s", "measures": [{"label": "Booting", "start": 1750528287.588426, "relative_start": 0, "end": 1750528288.108935, "relative_end": 1750528288.108935, "duration": 0.5205090045928955, "duration_str": "521ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750528288.109135, "relative_start": 0.5207087993621826, "end": **********.415882, "relative_end": 3.0994415283203125e-06, "duration": 1.3067471981048584, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48614136, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0010899999999999998, "accumulated_duration_str": "1.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3825269, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 55.046}, {"sql": "select * from `teams` where `slug` = 'bdc-school' limit 1", "type": "query", "params": [], "bindings": ["bdc-school"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.387221, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 55.046, "width_percent": 44.954}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Team": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": {"app.filament.app.widgets.parent.parent-overview-widget #IbtVncdF1TfpcYLMA0yN": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.app.widgets.parent.parent-overview-widget\"\n  \"component\" => \"App\\Filament\\App\\Widgets\\Parent\\ParentOverviewWidget\"\n  \"id\" => \"IbtVncdF1TfpcYLMA0yN\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T55Hd6wl3x7DKvwgYpHK37cqpRecYuDkGhMfaayr", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "5", "password_hash_web": "$2y$12$L5WhuvO3l7I4nzmwimPMKOimdn5mjfF7ebTPyIOvfoGB/qbishO7u", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/app/bdc-school\"\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-220529209 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-220529209\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1343769403 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1343769403\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1641100048 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T55Hd6wl3x7DKvwgYpHK37cqpRecYuDkGhMfaayr</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"295 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;IbtVncdF1TfpcYLMA0yN&quot;,&quot;name&quot;:&quot;app.filament.app.widgets.parent.parent-overview-widget&quot;,&quot;path&quot;:&quot;app\\/bdc-school&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;7015620e736647fc546a9a075507e010b53970ff87469e6f9ed9441670e0eb96&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641100048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-980982650 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">440</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/app/bdc-school</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IldmRlJwQUJHSWtyK3B1T2xqcXJnK3c9PSIsInZhbHVlIjoiVEZ1RDZXVmxNeEovaWl4dkhkUlkvWkR4MkJTTHVCTzdPR1FaM0JQbmNQWDNYdWphNkNkNUtXY0F3bnlwU3JXUUJQSjdndS9kYTNzYzBOTlFCWkphYjlmSzluVVY4NDYwM3E4d2VqS2lKNXgzVzNObDdwanBXSm4vZnRCVTFWWnNPcE91U014Ky82SkgwWElUMnJDYzJZbVBxWERtckVPUkJFZFYrM2tDenhhRHkzWjdTT204SEZnYUxCNi9iVktzdndKTjQ4bTRyWjlIZC9OUXd1dnJ1Y1lqamxVTEptN2ExOHFVSk55NWhxdz0iLCJtYWMiOiJjNDExYTllNGJmNWEwZGZmNDlhYjZjNWVmYmJlYzRjMzlmNDNhZGRhZDVjNzZhZTBmMTczYmNiNzNiMGI4NjRlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNMZmlPSkM4R2lNOFNrZU9adFUzekE9PSIsInZhbHVlIjoiaGFXcll5aThWSlFiVWFpSmlrVW9QaDVXS2l6SVhOMXAvM01WNzZuWVVCZ1o1UFBBR1hvS25KRjRhQy9yMVByYkMrMk1vNlJYaUxuWkdaQ0llcHFzQXE3UjZVQkV5SXVqYzh4Y2psM0lVZ2ZBTnRmTzlxYWNqeVo4SkhaMGRzR0oiLCJtYWMiOiI3NWFiMWNlNGU5M2UxMDFmN2I2NmI2YzhjNjZlYjIxNzEzMjU1MmY2Yjc5OTEyNDBkYWRkYjMyOWUzYzNkODcyIiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6InZHd215TEZmSkc5N3Z4bVVHbVI4eHc9PSIsInZhbHVlIjoidU1qamUyUWVlZ0Z6L3o2Q2hodnJscEpFa2Nwc2Uxb05hd3F4ZG9PYmF3K3RkaUtDaG9DNzByNlhMMFBHWjN2eC9FRDU0Mm0rQXhDbFd2Ympqdit4cFJvOGpBR1ZDZkNMS2kzUDFpYzRuNjI1dEM0WE42OGNpSG5EMWdJUVdVYVMiLCJtYWMiOiJkNDc5OGJhZDQ4NTVjYzY5NTkzMzgyMGZjZDhlMzg3YTJkNmIwMTE2NDU3ODgwMzA0ZWU4ZDQ2NzY5MTJkNDJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980982650\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-974432323 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|lPViIvJTGUiBtRMmY1VNjjfpo1OXpUfOsBZaJD1jnXpPVfsvKRHNRTwUE4Zz|$2y$12$L5WhuvO3l7I4nzmwimPMKOimdn5mjfF7ebTPyIOvfoGB/qbishO7u</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T55Hd6wl3x7DKvwgYpHK37cqpRecYuDkGhMfaayr</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dEQlOjqZw50WcW31adZ9t43fpbgOLfjFmA4izufq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974432323\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-981107082 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:51:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImtsZjNvQW11R3JjZ1hYNWJHYVlIeUE9PSIsInZhbHVlIjoiKzdKVUpPWnM3VWx2S0sxUlkzWWlsc0c0eHYyOXNKc0R3Q0ZRK3lxbnUzbTIxZzRUWGtDT0pXSXVaUi9CRE5SODhIZUFOcHhUSUZpSG5GQitBYUJiT2xOR3l4bEZ5RlNYYzNYbUtjSmRNaktheEJTSmVUUXpjZjl3RnJkdk4yR20iLCJtYWMiOiI4ZWU5MzVhM2VhYzM2YzU1MzlkNDU4MjcyZTBmODcyYzgyMjBkYWQ2YjNmZDg2OWRiNmEyNWNjNmZmZjA3M2E3IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:51:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IktQMGxIak9UWVFmMVFuNEZpYVhhb0E9PSIsInZhbHVlIjoiamZVTmw0STZablhqYnZoS3M1QncyZ2NMZUZXMlRCTEFGM3d1ZXVTa203U3I5TXpwb21MTE9PUDhmRTloNmVoWXVYS3pQZ0x3SFl4eWJmSjM5MktPMnhPZWJVQ3BNZEppMEtHQUdaUDUvRzB6cXdOZnhWYTVML1M3QTlIVEkzQ3UiLCJtYWMiOiJmMWY1MWNkYmYyMjE4YjMzZmVjZjhkNGVhYjkzN2E4NzE1MzQ5M2E2MTFlZDY5NWQxOTZmNDNlZDcyMjJjOThlIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:51:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImtsZjNvQW11R3JjZ1hYNWJHYVlIeUE9PSIsInZhbHVlIjoiKzdKVUpPWnM3VWx2S0sxUlkzWWlsc0c0eHYyOXNKc0R3Q0ZRK3lxbnUzbTIxZzRUWGtDT0pXSXVaUi9CRE5SODhIZUFOcHhUSUZpSG5GQitBYUJiT2xOR3l4bEZ5RlNYYzNYbUtjSmRNaktheEJTSmVUUXpjZjl3RnJkdk4yR20iLCJtYWMiOiI4ZWU5MzVhM2VhYzM2YzU1MzlkNDU4MjcyZTBmODcyYzgyMjBkYWQ2YjNmZDg2OWRiNmEyNWNjNmZmZjA3M2E3IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:51:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IktQMGxIak9UWVFmMVFuNEZpYVhhb0E9PSIsInZhbHVlIjoiamZVTmw0STZablhqYnZoS3M1QncyZ2NMZUZXMlRCTEFGM3d1ZXVTa203U3I5TXpwb21MTE9PUDhmRTloNmVoWXVYS3pQZ0x3SFl4eWJmSjM5MktPMnhPZWJVQ3BNZEppMEtHQUdaUDUvRzB6cXdOZnhWYTVML1M3QTlIVEkzQ3UiLCJtYWMiOiJmMWY1MWNkYmYyMjE4YjMzZmVjZjhkNGVhYjkzN2E4NzE1MzQ5M2E2MTFlZDY5NWQxOTZmNDNlZDcyMjJjOThlIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:51:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981107082\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1150536577 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T55Hd6wl3x7DKvwgYpHK37cqpRecYuDkGhMfaayr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$L5WhuvO3l7I4nzmwimPMKOimdn5mjfF7ebTPyIOvfoGB/qbishO7u</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/app/bdc-school</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150536577\", {\"maxDepth\":0})</script>\n"}}
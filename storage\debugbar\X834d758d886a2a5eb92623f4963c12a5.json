{"__meta": {"id": "X834d758d886a2a5eb92623f4963c12a5", "datetime": "2025-06-21 16:48:54", "utime": **********.709698, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750524533.660616, "end": **********.709723, "duration": 1.0491070747375488, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1750524533.660616, "relative_start": 0, "end": **********.091993, "relative_end": **********.091993, "duration": 0.4313771724700928, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.092008, "relative_start": 0.43139219284057617, "end": **********.709726, "relative_end": 3.0994415283203125e-06, "duration": 0.617717981338501, "duration_str": "618ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48614472, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00192, "accumulated_duration_str": "1.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.664862, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 26.042}, {"sql": "select * from `teams` where `slug` = 'bdc-school' limit 1", "type": "query", "params": [], "bindings": ["bdc-school"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.670455, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 26.042, "width_percent": 20.833}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 4 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 4, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6866229, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:75", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=75", "ajax": false, "filename": "DatabaseNotifications.php", "line": "75"}, "connection": "edu_db2", "explain": null, "start_percent": 46.875, "width_percent": 31.25}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 4 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 4, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6901698, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:97", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=97", "ajax": false, "filename": "DatabaseNotifications.php", "line": "97"}, "connection": "edu_db2", "explain": null, "start_percent": 78.125, "width_percent": 21.875}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Team": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": {"filament.livewire.database-notifications #eOwZmAXomSHVTbpslTj3": "array:4 [\n  \"data\" => array:1 [\n    \"paginators\" => array:1 [\n      \"database-notifications-page\" => 1\n    ]\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"eOwZmAXomSHVTbpslTj3\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sik3ZTFnhoJ3HNEqiMad2UH177321uL1bYJp2guc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "4", "password_hash_web": "$2y$12$6ALGmIi.KxN2hxd7o/geaO0MquJVeGk9I1K73Y8wB4Kgg5PogAhMe", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/app/bdc-school\"\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1628270941 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1628270941\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1656395360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1656395360\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sik3ZTFnhoJ3HNEqiMad2UH177321uL1bYJp2guc</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"341 characters\">{&quot;data&quot;:{&quot;paginators&quot;:[{&quot;database-notifications-page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;eOwZmAXomSHVTbpslTj3&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;app\\/bdc-school&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a24bd12fc0692a59c4cd4d94d0194c7fd009a009ad6ae451eb4d2a8a544e3bde&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-29068385 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">494</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/app/bdc-school</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,th;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IklXL0hHaDI5YWxxSXg4Rk4yTWJvZFE9PSIsInZhbHVlIjoiNG55QkpxSW1sUEdOamNjZXZIUXkrZnpvdzFUTjBIbVJJT3FlNFFYd0UwaVdqNkNtVGdkSDhxVDV5MjJ6c0EyU2hxRytuZTA3U1kxU1I1cnp6NzA5MVZaRU5DMGgzbDdjNk5TR0JLMzBwRUoxVXFQTXQyUTVMdkJEOS9Sdzczems1MzhmdnpJRlhSTnVpQkFKa0p5UXBVUHZLZ25EUHNjYStseHlrRFRjaVdtc2lwN0toTnZaelNkRVk5alVrZ1ZUbk40WWd2cmRJSXVKZGsvaWlybUZJYzBBUmRxSHhmVVYvTDFzZjhtbGVWcz0iLCJtYWMiOiJjYzRiOGE5YzZkNjM4NDk0MjI5ZmI0ZjQ0YTQ2ZjU2NTVkMTYwOGY4MGViOWU5NmQ0OTVhNmFlMDY0MzRlOGFjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlJPNkVzSGlvWElOSms0bks2TTZnRVE9PSIsInZhbHVlIjoiS3I3YXNJbUh2cEVaUTZyUmVlTVhudWpadWpHVms2SHN6eld5VFNpWVcyUEFqRGVJbzVpN09hRVhYSVppTXp4cXM4UGRCMGxUa0hRdTRMenFsemJZWEZXZjF1YzFualhnTXZMVXU4ZktvdHQ4SnpIb0ZHdzlpc3ROcDBoa2UxYm0iLCJtYWMiOiI0ZGRmYjE3ZTJlZDcxMmE1YTYwOTYxMjQ0YTA3ZjU0ZjU0ZmVjZjYyZDQ2ZmQ0NDM2OTAxNjQ2NTYwMzQ0YTBhIiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6ImFEY2Q1Wjg2ODFMamQzaWE4a2YyUFE9PSIsInZhbHVlIjoicSs3Z3JCQjRmT1JPMndPVFhHdzdNUnhQK2NmWEZMQ0g0bFgyNzhpVE5sUGVhTDU1RVpBNGN1NVRRNEdhN1VLUGhlS2dGSTNReWx4Z29ESUNqd2xJS3RIUTNmeW4zdGxWQUVpbVA0QmhiSFB5NDZTNjVHL05kaHBUWWhRWTI3a2YiLCJtYWMiOiJmZWU5OWU5OTEzYThkN2Y1N2U1MTA0ZGI1YTdlN2ZkMjA0MDk0NWExMGMyMTVkZGFhNzcyMjYzMGI1NzBlM2E1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29068385\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|wAbiF3uZFDQvtysZOyn19JYxYA5yfljrcnA2PMvY6EE3lMqqlihXRqXoFNLQ|$2y$12$6ALGmIi.KxN2hxd7o/geaO0MquJVeGk9I1K73Y8wB4Kgg5PogAhMe</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sik3ZTFnhoJ3HNEqiMad2UH177321uL1bYJp2guc</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gIb2uUwmfH7f7KKQvrSdVtw4xrVlMex5CtGZtKtp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1314424077 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 16:48:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjhmNDRIbjdPd2dHdzZMUFdobDl0U2c9PSIsInZhbHVlIjoiMHZKenZTTzFHbEY1RVRiZUVZS2FUVlRyL2syYW1hVVZXS01lMEk0N0NpaXVscVkzZlZvNmdUZFhuSWRNSWpSVm1lV3FiVzNQS0ZZUit5SS94NVE4bE1IaDF1RHp1dWYwbkFvTE5zblpTbU5zSHlwTkVzWjlyQ2dRbmVnVnhqajMiLCJtYWMiOiJiMzc4Njk1ZWY3YmQ4MWI3MjJkMTRhYmU5OWI1Y2FiYjY1MDQ2YmEzYjAxOTcyYzNlZWU1MzFkMWRhNTIxOGU1IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 18:48:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IjJRdk80ZlE2YzE5NFlhZkozOEkwL2c9PSIsInZhbHVlIjoidVY2VjRrRWFmK05RdTIzSElqdkR5NlFnbXc0NFlHZ3U2M0FMOE14eGQxbDFlWmR4MVVhdGhBYUU1K0xFMmZWTzhBRVZKU2tQRlltb1dNU0txakVna09RRnkwNUlGOVRmRDN0RmFzMFdickhqcVVBR29wNUliOW52WEl4UXlGa0IiLCJtYWMiOiJmYTAxNGU2Yzg3MzU5NWY4ZTFlMDNiOGZlODQ0NzRkMmI1NTk4ZjUzZGIxY2M2ZGYyMmNlNzY1YjhkMmU5ZTEwIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 18:48:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjhmNDRIbjdPd2dHdzZMUFdobDl0U2c9PSIsInZhbHVlIjoiMHZKenZTTzFHbEY1RVRiZUVZS2FUVlRyL2syYW1hVVZXS01lMEk0N0NpaXVscVkzZlZvNmdUZFhuSWRNSWpSVm1lV3FiVzNQS0ZZUit5SS94NVE4bE1IaDF1RHp1dWYwbkFvTE5zblpTbU5zSHlwTkVzWjlyQ2dRbmVnVnhqajMiLCJtYWMiOiJiMzc4Njk1ZWY3YmQ4MWI3MjJkMTRhYmU5OWI1Y2FiYjY1MDQ2YmEzYjAxOTcyYzNlZWU1MzFkMWRhNTIxOGU1IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 18:48:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IjJRdk80ZlE2YzE5NFlhZkozOEkwL2c9PSIsInZhbHVlIjoidVY2VjRrRWFmK05RdTIzSElqdkR5NlFnbXc0NFlHZ3U2M0FMOE14eGQxbDFlWmR4MVVhdGhBYUU1K0xFMmZWTzhBRVZKU2tQRlltb1dNU0txakVna09RRnkwNUlGOVRmRDN0RmFzMFdickhqcVVBR29wNUliOW52WEl4UXlGa0IiLCJtYWMiOiJmYTAxNGU2Yzg3MzU5NWY4ZTFlMDNiOGZlODQ0NzRkMmI1NTk4ZjUzZGIxY2M2ZGYyMmNlNzY1YjhkMmU5ZTEwIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 18:48:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314424077\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sik3ZTFnhoJ3HNEqiMad2UH177321uL1bYJp2guc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6ALGmIi.KxN2hxd7o/geaO0MquJVeGk9I1K73Y8wB4Kgg5PogAhMe</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/app/bdc-school</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}
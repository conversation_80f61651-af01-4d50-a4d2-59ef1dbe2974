{"__meta": {"id": "X6507cefbcc20b3583b52c576b9333649", "datetime": "2025-06-21 17:42:44", "utime": 1750527764.877599, "method": "GET", "uri": "/livewire/livewire.js?id=38dc8241", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750527764.102862, "end": 1750527764.87762, "duration": 0.7747581005096436, "duration_str": "775ms", "measures": [{"label": "Booting", "start": 1750527764.102862, "relative_start": 0, "end": 1750527764.46591, "relative_end": 1750527764.46591, "duration": 0.36304807662963867, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750527764.465918, "relative_start": 0.3630561828613281, "end": 1750527764.877623, "relative_end": 3.0994415283203125e-06, "duration": 0.41170501708984375, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48700088, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-512072518 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-512072518\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-260443348 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">38dc8241</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260443348\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-105910719 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-105910719\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1785741738 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,th;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imswb29jOWJiVTRUMk1DbHc2dzlkaHc9PSIsInZhbHVlIjoieEs1blJFUWJRMnhWaEVLcmVKdnRRQnNZcGlqdkx1dGNnMlVVSk9DSjlUalp6M1c5NFhSSWpLVS9ZcFE3VXhEdUhVQ3hkazByZXUzbmQ2bUdDZXorMUZ5aWlJTVp1NjhQaFliMU5NbmVsVC9WdENZRHU0NlpkT1VUaDRQY3IwaG5udnoxOTI4Y0xxc3NCTkRJbkQ5RWxrMGlSK2k0cjJ2dmMvWWZ1bDBWZC9OMzRQdU9CcEUxdVFnQ3ZNcTczK0RTZzBzT2lWaWduMHFUclQ5Wk1VS2UvdEVjL2NhRFIyRzRkN0o1Rmd2VzRKTT0iLCJtYWMiOiI5YjVmODhmOWU5YzA4M2E0YjMxNWE0NTlmNmNmZDg0YzZjMThmZDE5NzliOTA5NjI0Mjg2ODA0YmYwYTkwZWIzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlU3QUJHQm9Cc3E0TCtieFdTQ0FPRmc9PSIsInZhbHVlIjoiQjdsSUR5bTdxUlV0Y3hCZWFFTk0xQTFEVEpQSlN1a1NGaDlSUWlBM3A3NGlGSU0yK3RXQVpjbXRzcXhXY3lKOENBejZoZUR5QTdKUnpnNkRVQUZhRFh3UHhXVmQ0RHYxQVZTWHBIR0dsa3hQTVFoSTk2a1FDODh6TjJHcVVaMXIiLCJtYWMiOiJkZGFkNmMxOGNiNzRjNzk4MWMxMmM0M2EzOWNkMTY2MGNlNWZjYmQzYzdmM2M1NjVkZmMwNTgzNjhiOTY0ZmM2IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IkVzMkdUWXBOMVlST1pHZUtPTmh5aFE9PSIsInZhbHVlIjoiems4S3NGU00wRXBzNWVwZFAwSFpuWmRucjdtRzBzUkU4MDVRY1lPSWhSTjVEMlY5N2EzYWU1RVlWZmdoWnY2TjZLWXVESmtiMXJkdlJHSzBnN1pxUEQ1TG03dkdXVnE1U1BPelBvN3J6djRrb0M5dkRnQjNVTUhIeDFPY3pSOVgiLCJtYWMiOiJkOTNjZmZlMGQ4NWEwZjI2MjRlMWYzZTY4OWNlZGVhMzRiNjMzYTdhYzYyNWIyNTFjNDU4MjdlNjExMTgyMjJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785741738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-468351888 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6Imswb29jOWJiVTRUMk1DbHc2dzlkaHc9PSIsInZhbHVlIjoieEs1blJFUWJRMnhWaEVLcmVKdnRRQnNZcGlqdkx1dGNnMlVVSk9DSjlUalp6M1c5NFhSSWpLVS9ZcFE3VXhEdUhVQ3hkazByZXUzbmQ2bUdDZXorMUZ5aWlJTVp1NjhQaFliMU5NbmVsVC9WdENZRHU0NlpkT1VUaDRQY3IwaG5udnoxOTI4Y0xxc3NCTkRJbkQ5RWxrMGlSK2k0cjJ2dmMvWWZ1bDBWZC9OMzRQdU9CcEUxdVFnQ3ZNcTczK0RTZzBzT2lWaWduMHFUclQ5Wk1VS2UvdEVjL2NhRFIyRzRkN0o1Rmd2VzRKTT0iLCJtYWMiOiI5YjVmODhmOWU5YzA4M2E0YjMxNWE0NTlmNmNmZDg0YzZjMThmZDE5NzliOTA5NjI0Mjg2ODA0YmYwYTkwZWIzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlU3QUJHQm9Cc3E0TCtieFdTQ0FPRmc9PSIsInZhbHVlIjoiQjdsSUR5bTdxUlV0Y3hCZWFFTk0xQTFEVEpQSlN1a1NGaDlSUWlBM3A3NGlGSU0yK3RXQVpjbXRzcXhXY3lKOENBejZoZUR5QTdKUnpnNkRVQUZhRFh3UHhXVmQ0RHYxQVZTWHBIR0dsa3hQTVFoSTk2a1FDODh6TjJHcVVaMXIiLCJtYWMiOiJkZGFkNmMxOGNiNzRjNzk4MWMxMmM0M2EzOWNkMTY2MGNlNWZjYmQzYzdmM2M1NjVkZmMwNTgzNjhiOTY0ZmM2IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkVzMkdUWXBOMVlST1pHZUtPTmh5aFE9PSIsInZhbHVlIjoiems4S3NGU00wRXBzNWVwZFAwSFpuWmRucjdtRzBzUkU4MDVRY1lPSWhSTjVEMlY5N2EzYWU1RVlWZmdoWnY2TjZLWXVESmtiMXJkdlJHSzBnN1pxUEQ1TG03dkdXVnE1U1BPelBvN3J6djRrb0M5dkRnQjNVTUhIeDFPY3pSOVgiLCJtYWMiOiJkOTNjZmZlMGQ4NWEwZjI2MjRlMWYzZTY4OWNlZGVhMzRiNjMzYTdhYzYyNWIyNTFjNDU4MjdlNjExMTgyMjJhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468351888\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-6784233 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 21 Jun 2026 17:42:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Oct 2024 19:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:42:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">340160</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6784233\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1406473917 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1406473917\", {\"maxDepth\":0})</script>\n"}}
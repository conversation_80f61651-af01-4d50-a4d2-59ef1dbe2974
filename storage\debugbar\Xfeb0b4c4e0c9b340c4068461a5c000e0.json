{"__meta": {"id": "Xfeb0b4c4e0c9b340c4068461a5c000e0", "datetime": "2025-06-21 18:09:54", "utime": **********.4223, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750529393.66102, "end": **********.422323, "duration": 0.7613029479980469, "duration_str": "761ms", "measures": [{"label": "Booting", "start": 1750529393.66102, "relative_start": 0, "end": 1750529393.990501, "relative_end": 1750529393.990501, "duration": 0.3294808864593506, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750529393.990511, "relative_start": 0.32949090003967285, "end": **********.422325, "relative_end": 1.9073486328125e-06, "duration": 0.43181395530700684, "duration_str": "432ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48614504, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00235, "accumulated_duration_str": "2.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.384362, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 28.085}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.387654, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 28.085, "width_percent": 14.468}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 2 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 308}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}], "start": **********.392293, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "User.php:187", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=187", "ajax": false, "filename": "User.php", "line": "187"}, "connection": "edu_db2", "explain": null, "start_percent": 42.553, "width_percent": 17.021}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 2 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 2, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.404984, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:75", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=75", "ajax": false, "filename": "DatabaseNotifications.php", "line": "75"}, "connection": "edu_db2", "explain": null, "start_percent": 59.574, "width_percent": 27.234}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 2 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 2, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.408093, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:97", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=97", "ajax": false, "filename": "DatabaseNotifications.php", "line": "97"}, "connection": "edu_db2", "explain": null, "start_percent": 86.809, "width_percent": 13.191}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Team": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"filament.livewire.database-notifications #OziUWaFNrWL9Coyn91L6": "array:4 [\n  \"data\" => array:1 [\n    \"paginators\" => array:1 [\n      \"database-notifications-page\" => 1\n    ]\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"OziUWaFNrWL9Coyn91L6\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "password_hash_web": "$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/default-team/tasks\"\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1751685600 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1751685600\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-62317350 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-62317350\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-798537710 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"354 characters\">{&quot;data&quot;:{&quot;paginators&quot;:[{&quot;database-notifications-page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;OziUWaFNrWL9Coyn91L6&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;backend\\/default-team\\/tasks&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;5800d53f84192c8e6a1949b998078afe01f412cc548866605f05e0463389d9cf&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798537710\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-266678760 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">508</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlhMVXJOM3UxdElLRmdvZEo0QWg1WVE9PSIsInZhbHVlIjoiNzVhV3pab29VMDZVRWducTY1aHQzbDV5UzZLQ2hWd1ZmWldtWjFlSWY0V0hkNEpuYTZjSVhVL0pCYmNUTngvZmRCb0pUOTZlM0thT1VScWdCbUxydU9oeUlWbnpVTS8yeHd4U2VzWVhOclczYUE4WEhMWGdCRHFqVEkxVWlkVklsdE9JL2t1bnRVQjlXMHF2N2pzTlRJbi9JVGJQdk42U1hBU0xIcnUzZVN1TnhDL1pmU01mTUsyZndTOGtMYjJpWG5qNkY4WnNkUFhuRDlEejRrL0l5QjNWZEpLc3lFdXA3Sm5hVEJPWHFRWT0iLCJtYWMiOiI1YWU3ODA0MTJjNGZhOTI5NzE0YTFkNmI3NzA2MDYwZWY4ZmUxMjI3ZDMwYjA1MDA3MWU5Y2NlMGI5OTFmMzRmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik8wTFBTZDkvTU5INmdUaXlkWHgvUEE9PSIsInZhbHVlIjoiUDN5YTNwL3FtWkYzUHRMVnFjYXVKMGZCdmpITGk0QlBBTTMyR2oyQ2RRTWlHVk5Vb3Z4VUdSOVNkM1Rib1g2NTlHTWI4cWRGcTltUkhxdDYxYlpmdkFmRWNoWG50alNYZ1Jva0NhUGh5cWZKbjNXUXdNWGFrc01iUW1UV3RpQXYiLCJtYWMiOiIyMThlODcwOGZjODRmY2I5ZmRhYWU5Y2I5YWU3ZmQ2NDEwZGFmODkxODE0YWJhOTA2NjQyNzdmZTQwNTM5ZDM2IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6Iis1UVNjTnVCWDBlL0IyV0FBNkR1ZHc9PSIsInZhbHVlIjoidmVCQlE3TzM5U2tTeUFSalMrNmNIZkNWTkNxRHdOdnQrem5GNURZeWFmN05xMmZYdVREWkxQR3BVR0FrS2dGd3ZwV2FlVUtSRis2dFgxdjFXOEZGWHh4RzNERDY3OEZNYml3SnhaSktvZTcwc01FRW9aaE1hTmNTcmRwVWcyb2EiLCJtYWMiOiJhN2M0NDkwMmQ3MTZiMGQ4MGNiZDI4NjEzOGQ0ZjU5ODkwZmUyOGE3NDllNmUwMTc0MGMzMWNhODM3MzExYjY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266678760\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1533174468 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|ehyf0vWTi0nhH6BCaRuiRLr53ca7SdmqrBrbPg7dO3Vyvr6mhT3JSPnhkcqE|$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vsL2QWIgJub4i1B2dsBCWSF9PlW0AmpHCXRUUDpe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533174468\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-142580739 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 18:09:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlV1bnVYMTEvS2ozbUk0U01vTGNYZGc9PSIsInZhbHVlIjoiYzdnMmRyc2hLaERDcU1KWC9WRXFTOG9KQkcxSDJMeTdrY2RGaE9FNmZrdXQ2bkFTK3N1bERNZGpxSGJmRFB4UEg3N2k3UjJxc1hrNS81U3RHdlZqQmNQcjIrMFFmZTU2TlQyUGJjT3VBcXh4U3B1NHVNTFM5bkFWY1RSOTF1OEEiLCJtYWMiOiIwMDI3YzYwMDNlNDVhNmRmYzRmY2UyYjM5MzkwYjU1YzlmMjgxZjYxN2JkY2M2MTNhN2RhMzQ0ZGJlYTA1NGYzIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 20:09:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6Im9ScFJWZnJpZ1JWZmY5azU5c3QyUVE9PSIsInZhbHVlIjoiUW5jK084bU1zQm1uRVFWcHBsQzluSGhyd2VBTjY2ZjhqSXlWenZtMnZKdnhoZXhhQVhBSXdYamhGY3ZBVzNDei83SU1lWFBxaEM4Qk9xWGZMSHNaUkhHYzVadytlenhVOEFRemo2VUEwekg5dndpTCtpWXRGemt4VUhYVTd2ZHUiLCJtYWMiOiJhNzg3YzBmYmE2N2IyMjA5MmQzNGI5MTNlOTFiYmVlZWQyNWIzMzFjZWU3ZDcwMjU4ZWU5YmRmYWE4NTAyOTg0IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 20:09:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlV1bnVYMTEvS2ozbUk0U01vTGNYZGc9PSIsInZhbHVlIjoiYzdnMmRyc2hLaERDcU1KWC9WRXFTOG9KQkcxSDJMeTdrY2RGaE9FNmZrdXQ2bkFTK3N1bERNZGpxSGJmRFB4UEg3N2k3UjJxc1hrNS81U3RHdlZqQmNQcjIrMFFmZTU2TlQyUGJjT3VBcXh4U3B1NHVNTFM5bkFWY1RSOTF1OEEiLCJtYWMiOiIwMDI3YzYwMDNlNDVhNmRmYzRmY2UyYjM5MzkwYjU1YzlmMjgxZjYxN2JkY2M2MTNhN2RhMzQ0ZGJlYTA1NGYzIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 20:09:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6Im9ScFJWZnJpZ1JWZmY5azU5c3QyUVE9PSIsInZhbHVlIjoiUW5jK084bU1zQm1uRVFWcHBsQzluSGhyd2VBTjY2ZjhqSXlWenZtMnZKdnhoZXhhQVhBSXdYamhGY3ZBVzNDei83SU1lWFBxaEM4Qk9xWGZMSHNaUkhHYzVadytlenhVOEFRemo2VUEwekg5dndpTCtpWXRGemt4VUhYVTd2ZHUiLCJtYWMiOiJhNzg3YzBmYmE2N2IyMjA5MmQzNGI5MTNlOTFiYmVlZWQyNWIzMzFjZWU3ZDcwMjU4ZWU5YmRmYWE4NTAyOTg0IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 20:09:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142580739\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-902798256 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902798256\", {\"maxDepth\":0})</script>\n"}}
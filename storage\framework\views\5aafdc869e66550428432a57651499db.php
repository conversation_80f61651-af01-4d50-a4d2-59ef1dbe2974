<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            My Teaching Schedule
         <?php $__env->endSlot(); ?>

         <?php $__env->slot('headerEnd', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['tag' => 'a','href' => ''.e(route('filament.backend.resources.tasks.index', ['tenant' => filament()->getTenant()])).'','size' => 'sm','color' => 'gray','outlined' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'a','href' => ''.e(route('filament.backend.resources.tasks.index', ['tenant' => filament()->getTenant()])).'','size' => 'sm','color' => 'gray','outlined' => true]); ?>
                View All Tasks
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?> <div x-data="{ open: false }" class="relative inline-block">
  
         <?php $__env->endSlot(); ?>

        <div class="space-y-6">
            <!-- Compact Timetable -->
            <div class="timetable-grid bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                
                    <!-- Time column header -->
                    <div class="bg-gray-50 dark:bg-gray-700 px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 text-center">
                        Time
                    </div>

                    <!-- Day headers -->
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $dayName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $date = $startOfWeek->copy()->addDays($index);
                            $isToday = $date->isToday();
                        ?>
                        <div class="timetable-cell bg-gray-50 dark:bg-gray-700 px-2 py-1 text-xs font-medium text-center <?php echo e($isToday ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30' : 'text-gray-500 dark:text-gray-400'); ?>">
                            <div><?php echo e($dayName); ?></div>
                            <div class="text-xs <?php echo e($isToday ? 'text-blue-500' : 'text-gray-400'); ?>">
                                <?php echo e($date->format('j')); ?>

                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                

                <!-- Time slots (compact - only show hours with content or key hours) -->
                <?php
                    $keyHours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18];
                    $hasContentHours = [];

                    // Find hours that have content
                    foreach($weekData as $dayData) {
                        foreach($dayData['slots'] as $timeSlot => $items) {
                            if (!empty($items)) {
                                $hour = (int) substr($timeSlot, 0, 2);
                                $hasContentHours[] = $hour;
                            }
                        }
                    }

                    $displayHours = array_unique(array_merge($keyHours, $hasContentHours));
                    sort($displayHours);
                ?>

                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $displayHours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $timeSlot = sprintf('%02d:00', $hour); ?>
                    
                        <!-- Time label -->
                        <div class="timetable-cell  bg-white dark:bg-gray-800 px-2 py-1 text-xs text-gray-500 dark:text-gray-400 text-center border-r">
                            <?php echo e(sprintf('%02d:00', $hour)); ?>

                        </div>

                        <!-- Day cells -->
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = range(0, 6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dayIndex): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $date = $startOfWeek->copy()->addDays($dayIndex);
                                $dateKey = $date->format('Y-m-d');
                                $items = $weekData[$dateKey]['slots'][$timeSlot] ?? [];
                                $isToday = $date->isToday();
                            ?>
                            <div class="timetable-cell relative <?php echo e($isToday ? 'bg-blue-50 dark:bg-blue-900/20' : ''); ?>">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div x-data="{showPopover: false,taskData: <?php echo \Illuminate\Support\Js::from($item)->toHtml() ?>}" class="relative inline-block"> 

                                    <div
                                        
                                        @mouseenter="showPopover = true"
                                        @mouseleave="showPopover = false"
                                        class="draggable-item <?php echo e($item['type'] === 'task' ? 'task-item' : 'schedule-item'); ?> priority-<?php echo e($item['priority'] ?? 'medium'); ?> cursor-pointer relative"
                                        data-id="<?php echo e($item['id']); ?>"
                                        data-type="<?php echo e($item['type']); ?>"
                                    >
                                        <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                            <?php echo e(Str::limit($item['title'], 15, "...")); ?>

                                        </div>
                                        <div class="text-gray-600 dark:text-gray-400 flex items-center gap-1 text-xs">
                                            <span class="font-mono font-medium"><?php echo e($item['start_time']->format('H:i')); ?></span>
                                            <!--[if BLOCK]><![endif]--><?php if($item['end_time']): ?>
                                                <span class="text-gray-500">-<?php echo e($item['end_time']->format('H:i')); ?></span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <!--[if BLOCK]><![endif]--><?php if(($item['priority'] ?? '') === 'high' || ($item['priority'] ?? '') === 'urgent'): ?>
                                                ‼️
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        
                                    </div>
                                     
                                    <!-- Alpine.js Popover -->
                                    <div
                                        x-show="showPopover"
                                        @click.outside="showPopover = false"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-y-1"
                                        x-transition:enter-end="opacity-100 translate-y-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-y-0"
                                        x-transition:leave-end="opacity-0 translate-y-1"
                                        class="absolute z-20 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg p-4 text-gray-700"
                                        style="min-width: 300px"
                                    >
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <h4 class="font-medium text-gray-900 dark:text-gray-100 text-sm" x-text="taskData.title"></h4>
                                                <span
                                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                                    :class="taskData.type === 'task' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'"
                                                    x-text="taskData.type === 'task' ? 'Task' : 'Class'"
                                                ></span>
                                            </div>

                                            <div class="space-y-2 text-sm">
                                                <!-- Time -->
                                                <div class="flex items-center text-gray-600 dark:text-gray-400">
                                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <span x-text="new Date(taskData.start_time).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: false}) + (taskData.end_time ? ' - ' + new Date(taskData.end_time).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: false}) : '')"></span>
                                                </div>

                                                <!-- Location -->
                                                <div x-show="taskData.location" class="flex items-center text-gray-600 dark:text-gray-400">
                                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    <span x-text="taskData.location"></span>
                                                </div>

                                                <!-- Priority -->
                                                <div x-show="taskData.priority" class="flex items-center text-gray-600 dark:text-gray-400">
                                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
                                                    </svg>
                                                    <span x-text="'Priority: ' + (taskData.priority ? taskData.priority.charAt(0).toUpperCase() + taskData.priority.slice(1) : '')"></span>
                                                </div>

                                                <!-- Status -->
                                                <div x-show="taskData.status" class="flex items-center text-gray-600 dark:text-gray-400">
                                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <span x-text="'Status: ' + (taskData.status ? taskData.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : '')"></span>
                                                </div>

                                                <!-- Assigned To -->
                                                <div x-show="taskData.assigned_to" class="flex items-center text-gray-600 dark:text-gray-400">
                                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                    <span x-text="'Assigned to: ' + taskData.assigned_to"></span>
                                                </div>

                                                <!-- Description -->
                                                <div x-show="taskData.description || taskData.notes" class="text-gray-600 dark:text-gray-400 text-xs mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                                                    <div class="font-medium mb-1">Description:</div>
                                                    <div x-text="taskData.description || taskData.notes"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Today's Tasks Table -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Today's Tasks (<?php echo e($today->format('M j, Y')); ?>)
                    </h3>
                    <!--[if BLOCK]><![endif]--><?php if($totalTodayTasks > 5): ?>
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                            Showing 5 of <?php echo e($totalTodayTasks); ?> tasks
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                
                <!--[if BLOCK]><![endif]--><?php if($todayTasks->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Time</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Task</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Location</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Priority</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $todayTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                <?php echo e($task->start_datetime->format('H:i')); ?>

                                                <!--[if BLOCK]><![endif]--><?php if($task->end_datetime): ?>
                                                    - <?php echo e($task->end_datetime->format('H:i')); ?>

                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                <?php echo e($task->title); ?>

                                            </div>
                                            <!--[if BLOCK]><![endif]--><?php if($task->description): ?>
                                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    <?php echo e(Str::limit($task->description, 50)); ?>

                                                </div>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?php echo e($task->location ?: '-'); ?>

                                            </div>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php echo e($task->priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : ''); ?>

                                                <?php echo e($task->priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''); ?>

                                                <?php echo e($task->priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : ''); ?>

                                                <?php echo e($task->priority === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''); ?>

                                            ">
                                                <?php echo e(ucfirst($task->priority)); ?>

                                            </span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php echo e($task->status === 'pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' : ''); ?>

                                                <?php echo e($task->status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''); ?>

                                            ">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="px-4 py-8 text-center">
                        <div class="text-gray-500 dark:text-gray-400">
                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-calendar-days'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-8 h-8 mx-auto mb-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            <p class="text-sm">No tasks scheduled for today</p>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- View More Button -->
            <div class="text-center">
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['tag' => 'a','href' => ''.e(route('filament.backend.resources.tasks.index', ['tenant' => filament()->getTenant()])).'','color' => 'primary','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'a','href' => ''.e(route('filament.backend.resources.tasks.index', ['tenant' => filament()->getTenant()])).'','color' => 'primary','size' => 'sm']); ?>
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-calendar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 mr-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                    View Full Timetable
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>



    <style>
        
        .time-slot {
            border-bottom: 1px solid #e5e7eb;
        }

        .time-slot:last-child {
            border-bottom: none;
        }

        .timetable-grid {
            display: grid;
            grid-template-columns: 80px repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
        }



        .timetable-cell {
            background-color: white;
            min-height: 60px;
            padding: 4px;
            position: relative;
        }

        .dark .timetable-cell {
            background-color: #374151;
        }

        .dark .timetable-grid {
            background-color: #4b5563;
        }
        .draggable-item {
            cursor: pointer;
            transition: opacity 0.2s ease, transform 0.2s ease;
        }

        .draggable-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            z-index: 5;
            filter: brightness(1.05);
        }

        .drop-zone {
            min-height: 60px;
            transition: background-color 0.2s ease;
        }

        .task-item {
            border-radius: 4px;
            padding: 4px 8px;
            margin: 1px 0;
            font-size: 12px;
            line-height: 1.3;
            border-left: 3px solid;
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            position: relative;
            overflow: hidden;
        }

        .task-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.05));
        }

        .task-item.priority-high {
            border-left-color: #EF4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        .task-item.priority-urgent {
            border-left-color: #DC2626;
            background-color: rgba(220, 38, 38, 0.15);
        }

        .task-item.priority-medium {
            border-left-color: #F59E0B;
            background-color: rgba(245, 158, 11, 0.1);
        }

        .task-item.priority-low {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .schedule-item {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }


    </style>




 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/filament/widgets/teacher-timetable-widget.blade.php ENDPATH**/ ?>
{"__meta": {"id": "X5f0f64bd4f6477f7ab6d097050604848", "datetime": "2025-06-21 17:42:35", "utime": 1750527755.018332, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.028613, "end": 1750527755.018359, "duration": 0.9897458553314209, "duration_str": "990ms", "measures": [{"label": "Booting", "start": **********.028613, "relative_start": 0, "end": **********.422051, "relative_end": **********.422051, "duration": 0.39343786239624023, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.422061, "relative_start": 0.3934478759765625, "end": 1750527755.018362, "relative_end": 3.0994415283203125e-06, "duration": 0.5963010787963867, "duration_str": "596ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48627480, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 55, "nb_visible_statements": 55, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.031660000000000015, "accumulated_duration_str": "31.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.823588, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 1.358}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.827453, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 1.358, "width_percent": 0.821}, {"sql": "select count(*) as aggregate from `users` where `team_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.838732, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:31", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=31", "ajax": false, "filename": "TeamStatsWidget.php", "line": "31"}, "connection": "edu_db2", "explain": null, "start_percent": 2.179, "width_percent": 1.263}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.842784, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:32", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=32", "ajax": false, "filename": "TeamStatsWidget.php", "line": "32"}, "connection": "edu_db2", "explain": null, "start_percent": 3.443, "width_percent": 1.674}, {"sql": "select count(*) as aggregate from `shop_products` where `team_id` = 1 and `shop_products`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.8461459, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=33", "ajax": false, "filename": "TeamStatsWidget.php", "line": "33"}, "connection": "edu_db2", "explain": null, "start_percent": 5.117, "width_percent": 0.79}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.849077, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:34", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=34", "ajax": false, "filename": "TeamStatsWidget.php", "line": "34"}, "connection": "edu_db2", "explain": null, "start_percent": 5.907, "width_percent": 1.611}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` >= '2025-05-22 17:42:34' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-05-22 17:42:34", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.851916, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:39", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=39", "ajax": false, "filename": "TeamStatsWidget.php", "line": "39"}, "connection": "edu_db2", "explain": null, "start_percent": 7.517, "width_percent": 1.674}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `created_at` >= '2025-05-22 17:42:34' and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-05-22 17:42:34", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.854886, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:43", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=43", "ajax": false, "filename": "TeamStatsWidget.php", "line": "43"}, "connection": "edu_db2", "explain": null, "start_percent": 9.191, "width_percent": 1.674}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.867437, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 10.865, "width_percent": 1.137}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` <= '2025-06-21 17:42:34' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:42:34", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 71}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.872946, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:71", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=71", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "71"}, "connection": "edu_db2", "explain": null, "start_percent": 12.003, "width_percent": 2.716}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` <= '2025-06-21 17:42:34' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:42:34", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 72}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.875476, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:72", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=72", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "72"}, "connection": "edu_db2", "explain": null, "start_percent": 14.719, "width_percent": 2.906}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `created_at` <= '2025-06-21 17:42:34' and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:42:34", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 73}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.877909, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:73", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=73", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "73"}, "connection": "edu_db2", "explain": null, "start_percent": 17.625, "width_percent": 2.148}, {"sql": "select count(*) as aggregate from `users` where `team_id` = 1 and `created_at` <= '2025-06-21 17:42:34'", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:42:34"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.879987, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=74", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "74"}, "connection": "edu_db2", "explain": null, "start_percent": 19.773, "width_percent": 0.758}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.887259, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 20.531, "width_percent": 1.327}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.890618, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 21.857, "width_percent": 2.59}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.89345, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 24.447, "width_percent": 2.685}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.895932, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 27.132, "width_percent": 2.401}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.898441, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 29.533, "width_percent": 2.716}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.900945, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 32.249, "width_percent": 1.579}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.90308, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 33.828, "width_percent": 2.053}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.906229, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 35.881, "width_percent": 1.895}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9085572, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 37.776, "width_percent": 1.516}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.910816, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 39.292, "width_percent": 1.579}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.913053, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 40.872, "width_percent": 1.548}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.915157, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 42.419, "width_percent": 1.485}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.917243, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 43.904, "width_percent": 1.485}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.924325, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 45.389, "width_percent": 1.358}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.927085, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 46.747, "width_percent": 2.369}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.929561, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 49.116, "width_percent": 1.706}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.931765, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 50.821, "width_percent": 2.748}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.934271, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 53.569, "width_percent": 1.611}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9364, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 55.18, "width_percent": 1.611}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.93853, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 56.791, "width_percent": 2.843}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9410088, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 59.634, "width_percent": 1.327}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9431348, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 60.96, "width_percent": 1.358}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9452531, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 62.318, "width_percent": 2.021}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9474778, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 64.34, "width_percent": 1.358}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9494839, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 65.698, "width_percent": 1.327}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.951465, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 67.025, "width_percent": 1.327}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.95646, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 68.351, "width_percent": 0.948}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.95917, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 69.299, "width_percent": 2.622}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.961705, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 71.92, "width_percent": 1.706}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.96387, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 73.626, "width_percent": 1.579}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.966007, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 75.205, "width_percent": 1.674}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.968166, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 76.879, "width_percent": 1.579}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.970231, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 78.459, "width_percent": 1.548}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.972278, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 80.006, "width_percent": 1.548}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.975569, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 81.554, "width_percent": 2.306}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.978399, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 83.86, "width_percent": 2.369}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9815621, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 86.229, "width_percent": 3.127}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.985188, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 89.356, "width_percent": 3.348}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9890752, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 92.704, "width_percent": 3.127}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.996826, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 95.831, "width_percent": 1.232}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 8 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 8, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": 1750527755.0019841, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:75", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=75", "ajax": false, "filename": "DatabaseNotifications.php", "line": "75"}, "connection": "edu_db2", "explain": null, "start_percent": 97.063, "width_percent": 1.516}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 8 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 8, "filament"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": 1750527755.0045571, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:97", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=97", "ajax": false, "filename": "DatabaseNotifications.php", "line": "97"}, "connection": "edu_db2", "explain": null, "start_percent": 98.579, "width_percent": 1.421}]}, "models": {"data": {"App\\Models\\Team": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 7, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.team-stats-widget #m9gdSf2NyQrwyY1DHY13": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.team-stats-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\TeamStatsWidget\"\n  \"id\" => \"m9gdSf2NyQrwyY1DHY13\"\n]", "app.filament.widgets.stats-overview-widget #eDz4B2As1Neh0d32A2Tj": "array:4 [\n  \"data\" => array:1 [\n    \"filters\" => array:3 [\n      \"businessCustomersOnly\" => null\n      \"startDate\" => null\n      \"endDate\" => null\n    ]\n  ]\n  \"name\" => \"app.filament.widgets.stats-overview-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\StatsOverviewWidget\"\n  \"id\" => \"eDz4B2As1Neh0d32A2Tj\"\n]", "app.filament.widgets.orders-chart #Dwsjc4tfYjHxndVpztua": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"fc55227bc58354bd4f71131d90bfd841\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.orders-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\OrdersChart\"\n  \"id\" => \"Dwsjc4tfYjHxndVpztua\"\n]", "app.filament.widgets.customers-chart #TwLmcwtoqtksmmsh0V8f": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"95e447feb0484e3734e406fc9a599444\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.customers-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\CustomersChart\"\n  \"id\" => \"TwLmcwtoqtksmmsh0V8f\"\n]", "app.filament.widgets.revenue-chart #UksKQm6ap6LPeB5dpKP2": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"97ab313d40eee230c600543f6d085ec0\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueChart\"\n  \"id\" => \"UksKQm6ap6LPeB5dpKP2\"\n]", "filament.livewire.database-notifications #1CxfVRZ4WTbN9zVtcHct": "array:4 [\n  \"data\" => array:1 [\n    \"paginators\" => array:1 [\n      \"database-notifications-page\" => 1\n    ]\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"1CxfVRZ4WTbN9zVtcHct\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/default-team\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "8", "password_hash_web": "$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe", "Dashboard_filters": "array:3 [\n  \"businessCustomersOnly\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-661926206 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-661926206\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1551875384 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1551875384\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1976609946 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"285 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;m9gdSf2NyQrwyY1DHY13&quot;,&quot;name&quot;:&quot;app.filament.widgets.team-stats-widget&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;ee9cc812fd97cea50421562fd81319d94ec1903cf924710412b2395a0e2902c9&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"395 characters\">{&quot;data&quot;:{&quot;filters&quot;:[{&quot;businessCustomersOnly&quot;:null,&quot;startDate&quot;:null,&quot;endDate&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;eDz4B2As1Neh0d32A2Tj&quot;,&quot;name&quot;:&quot;app.filament.widgets.stats-overview-widget&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;ffb6f05fd5bc688b040d8d14c8cdf6b1592ddb0a7e0a3cd607d18216eee21600&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"343 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;fc55227bc58354bd4f71131d90bfd841&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;Dwsjc4tfYjHxndVpztua&quot;,&quot;name&quot;:&quot;app.filament.widgets.orders-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;57e7ad835b5c549a1411bdc712baad28cba4821d978d3afb97d3e92cef95f87b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;95e447feb0484e3734e406fc9a599444&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;TwLmcwtoqtksmmsh0V8f&quot;,&quot;name&quot;:&quot;app.filament.widgets.customers-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;43934ebd60c84210d2601e2e2d8e12172b2721102828754764177702254cf07e&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;97ab313d40eee230c600543f6d085ec0&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;UksKQm6ap6LPeB5dpKP2&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;6bb7e976df934ea1609f2e62a42deb3b28c930e717bbd7e5181de12031cdfa48&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;data&quot;:{&quot;paginators&quot;:[{&quot;database-notifications-page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;1CxfVRZ4WTbN9zVtcHct&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;ed40cd1994277abc8c223ea51df8d73ab94c09834fbc472f9cc74b0741ceb6c9&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976609946\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-912674492 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2782</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,th;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imswb29jOWJiVTRUMk1DbHc2dzlkaHc9PSIsInZhbHVlIjoieEs1blJFUWJRMnhWaEVLcmVKdnRRQnNZcGlqdkx1dGNnMlVVSk9DSjlUalp6M1c5NFhSSWpLVS9ZcFE3VXhEdUhVQ3hkazByZXUzbmQ2bUdDZXorMUZ5aWlJTVp1NjhQaFliMU5NbmVsVC9WdENZRHU0NlpkT1VUaDRQY3IwaG5udnoxOTI4Y0xxc3NCTkRJbkQ5RWxrMGlSK2k0cjJ2dmMvWWZ1bDBWZC9OMzRQdU9CcEUxdVFnQ3ZNcTczK0RTZzBzT2lWaWduMHFUclQ5Wk1VS2UvdEVjL2NhRFIyRzRkN0o1Rmd2VzRKTT0iLCJtYWMiOiI5YjVmODhmOWU5YzA4M2E0YjMxNWE0NTlmNmNmZDg0YzZjMThmZDE5NzliOTA5NjI0Mjg2ODA0YmYwYTkwZWIzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVjS3NUVWJ6T2l6VHpuKzJVRkN2OUE9PSIsInZhbHVlIjoiQzc5cG96ZWJFZ0w1d1E1UWRnUnVtRWY5SGROK0t0ZFlqbUl1OHhGV09xdWpLNWRYNHFJQXZTUWR6Wm9qeU9oTjdtZGV1WExFcTRpZTdoVkVwTDNLNlpQQWJMdE9rRlR6MXh1REJ2M3k0cW03MW01MjRIbEVrbWFTbkRhUXU4MG4iLCJtYWMiOiJhZDdjMTZhYWI2MzgzNzdhNjZiYWQ4NWE1NTI0Y2ZjOGI1MWFkZDFjOTI1Y2EwMmVlOTNiZWQ1NTU0MzRlZTUzIiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6Iks0SjhCZzhxbkx4dC90RlpxYmRaK0E9PSIsInZhbHVlIjoiVUFtUnlzL3lCUDZMTHBOQmF4b2tyQXBCeUhVU2NCanp6ejRqaDlPOHI2a2lXOUcwdnRzYklhNEVVdCsyMkJSVzZiRUhoVDdnWUVSbDhlTmVsWlY5YUZCVzI1ajFDS1haMU9GUVRybDJ3amd0YlNDQkxxTHhqTDBKOGx0WU1UcG8iLCJtYWMiOiI4MjkxN2FkMGU5MWVjMzRiOGI4MjFhZjUzNzhiNjI4OTQ2MGY0MWU2ZmM3Mzg5NjBiYTU4YjJlZjgxNmI1MTc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-912674492\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-407636167 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">8|uEm0mlZkc74gIicIsEcLEvV0115i1N3Y0ZTpeVcevaERH48GCO4oF8pIiIWH|$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bDakuFXZEjwkzwceMwuwBZu7yieBSY9wGZVonsnp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407636167\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1095779634 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:42:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImoweG9EejBrTHN0Rno3OVRXd0xYakE9PSIsInZhbHVlIjoiRnZSQlZNcFo2blorUXMxWDR2WjJpTnkxYWxZazBpTEpuQTlWSXVXVUljRlVHRlhMYkVzeHRXZDVCYmNqWENoZUw2RG5XODhueDk2MWZaWHBCeHRrOGhPaVVaM1Z4eTRKMjV4MHJUL05JVGJ4bUl5NURJZ1RsUmRNb1JFN1VvZzQiLCJtYWMiOiI0ZTExM2IzZTcyN2FkYjVlN2VhZjc4ZTcwZGVmZDY1MWJiYTJhOTdhYjU5ZTg3YTJhY2Q2YTliODJkMTA2M2EzIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:42:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6Ijhab1J0RG9zbXdlYVF6dXEwdTZaZ1E9PSIsInZhbHVlIjoiNnpDc0JaSkVZOEE4Q1RIZ2xZS3Y1V2wwU2RuS2YrNlllN2VRYUxIbFo1WGRWUXFkblYwZmRwM2c3dVY0bFZSSkJZcStlNi9kLzBlN3c3NUx3dW9QbmVpNW5oK2JsZWlMR29HOGNQMmo3TGwzL2Y1ZUpyazBYcXpmaWZ1WWVTdlAiLCJtYWMiOiIzNzY4Y2M0MmUyMzcxMWMwM2Q1NmIyOTNmM2NkZWY0Yzg1YjQ1MDdmZDIyNDkyZTdjOGY3YWU2MGVjMTVjOGI3IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:42:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImoweG9EejBrTHN0Rno3OVRXd0xYakE9PSIsInZhbHVlIjoiRnZSQlZNcFo2blorUXMxWDR2WjJpTnkxYWxZazBpTEpuQTlWSXVXVUljRlVHRlhMYkVzeHRXZDVCYmNqWENoZUw2RG5XODhueDk2MWZaWHBCeHRrOGhPaVVaM1Z4eTRKMjV4MHJUL05JVGJ4bUl5NURJZ1RsUmRNb1JFN1VvZzQiLCJtYWMiOiI0ZTExM2IzZTcyN2FkYjVlN2VhZjc4ZTcwZGVmZDY1MWJiYTJhOTdhYjU5ZTg3YTJhY2Q2YTliODJkMTA2M2EzIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:42:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6Ijhab1J0RG9zbXdlYVF6dXEwdTZaZ1E9PSIsInZhbHVlIjoiNnpDc0JaSkVZOEE4Q1RIZ2xZS3Y1V2wwU2RuS2YrNlllN2VRYUxIbFo1WGRWUXFkblYwZmRwM2c3dVY0bFZSSkJZcStlNi9kLzBlN3c3NUx3dW9QbmVpNW5oK2JsZWlMR29HOGNQMmo3TGwzL2Y1ZUpyazBYcXpmaWZ1WWVTdlAiLCJtYWMiOiIzNzY4Y2M0MmUyMzcxMWMwM2Q1NmIyOTNmM2NkZWY0Yzg1YjQ1MDdmZDIyNDkyZTdjOGY3YWU2MGVjMTVjOGI3IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:42:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095779634\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-643815296 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>Dashboard_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>businessCustomersOnly</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643815296\", {\"maxDepth\":0})</script>\n"}}
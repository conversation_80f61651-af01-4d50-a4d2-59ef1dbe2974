{"__meta": {"id": "X02c8cb18f57524452c4f72f151e09c3e", "datetime": "2025-06-21 16:59:10", "utime": **********.572019, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750525149.613256, "end": **********.572047, "duration": 0.9587910175323486, "duration_str": "959ms", "measures": [{"label": "Booting", "start": 1750525149.613256, "relative_start": 0, "end": 1750525149.977766, "relative_end": 1750525149.977766, "duration": 0.36451005935668945, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750525149.977779, "relative_start": 0.36452293395996094, "end": **********.572049, "relative_end": 1.9073486328125e-06, "duration": 0.5942699909210205, "duration_str": "594ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48619712, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\RevenueChart@updateChartData", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>"}, "queries": {"nb_statements": 27, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01809, "accumulated_duration_str": "18.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4767501, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 2.543}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4807038, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 2.543, "width_percent": 2.322}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.489925, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 4.865, "width_percent": 4.035}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.493338, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 8.9, "width_percent": 3.648}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.4962318, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 12.548, "width_percent": 6.191}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.499213, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 18.74, "width_percent": 2.985}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.501745, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 21.725, "width_percent": 3.372}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.504149, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 25.097, "width_percent": 3.04}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.506509, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 28.137, "width_percent": 4.92}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5094812, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 33.057, "width_percent": 4.533}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.512124, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 37.59, "width_percent": 5.417}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5151708, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 43.007, "width_percent": 5.141}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5186162, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 48.148, "width_percent": 3.538}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.521112, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 51.686, "width_percent": 2.875}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.531584, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 54.561, "width_percent": 1.99}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.534851, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 56.551, "width_percent": 3.704}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5375228, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 60.254, "width_percent": 3.317}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.540074, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 63.571, "width_percent": 3.704}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.542631, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 67.275, "width_percent": 2.985}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.544894, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 70.26, "width_percent": 5.196}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.548439, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 75.456, "width_percent": 4.035}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5518322, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 79.491, "width_percent": 3.483}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5543642, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 82.974, "width_percent": 4.478}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5572171, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 87.452, "width_percent": 2.819}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.559497, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 90.271, "width_percent": 2.543}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.561819, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 92.814, "width_percent": 3.925}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5644748, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 96.739, "width_percent": 3.261}]}, "models": {"data": {"App\\Models\\Team": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.revenue-chart #CqVX9mu99tkhctHHoEH7": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"97ab313d40eee230c600543f6d085ec0\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueChart\"\n  \"id\" => \"CqVX9mu99tkhctHHoEH7\"\n]", "app.filament.widgets.customers-chart #IyuRtDMg0X4hPOxyebEG": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"95e447feb0484e3734e406fc9a599444\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.customers-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\CustomersChart\"\n  \"id\" => \"IyuRtDMg0X4hPOxyebEG\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "oJVQrpdUzynGiTnyeG8i2DIDXWmXKGLYbxNp8SYN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/backend/default-team\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "8", "password_hash_web": "$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe", "Dashboard_filters": "array:3 [\n  \"businessCustomersOnly\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1387738252 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1387738252\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1918184827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1918184827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-132854645 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJVQrpdUzynGiTnyeG8i2DIDXWmXKGLYbxNp8SYN</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;97ab313d40eee230c600543f6d085ec0&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;CqVX9mu99tkhctHHoEH7&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;af7876c8b79978cb13327ae5750f2b70ff1fc9df0895f468c1e05f518fd5cd09&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;95e447feb0484e3734e406fc9a599444&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;IyuRtDMg0X4hPOxyebEG&quot;,&quot;name&quot;:&quot;app.filament.widgets.customers-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;cbe7541b03d20fd1a03a350e30ba1dbbeb999de9a9d4f8aed7295d8062994cd4&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132854645\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-192744078 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1024</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,th;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik1YTktiNk5Fckx4TS9WZkxtRmpCWlE9PSIsInZhbHVlIjoid2RpVGtXdE5kRUUzSFZ4Umd3WWNzZmZVeWxXTmNUSzlITDNLNGE5VXpMSFR5ajV1cExnWWg3QVVUTWV2c3RRVEx4VlRuZ09YRFcwUktKMUxjVm4zTkVZSkpIUE5SL1JxM0cxc1JOdGlPZUU0eHkyalhSaGR6eGxGQ2ozYks0T2E0VXFxcE9XNHpQdWJHSTNCYnVuelFrRHlMUElJMEJaMlBUaFVQeHczK2xHRlFKTTRTVEhxYVQweTFwY24wTlJKTEZFSWczdFpyemh0UWFGd0ZPdk5kdWlHS3pkRGx5YTErTVJoTDhwMEVZYz0iLCJtYWMiOiI0ODYwNTc5OTAwYzVhMjRjNzE1MzRjNzFiOTMzMzBjZmVjYjk3NmZlODBiZjYzYmQxNmFmNWY1Y2JjNjlkNTc1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkttQkNpZXJiRDRqNmw3cS80eDRReFE9PSIsInZhbHVlIjoiTW15NVV4akFGK2dEV0ZxYVhzMFpkSUgwL3VkcmJKdHUrY3RlbFRHYU5IcHpSSlI1Um9kditvV1pSaUpaeTFhVkVsNnBNbURUT3NLenB4dDhTbVpFOURDWnM1ZW1zN0V3KzdxQUxMTmtESmt2dythRy9RZFVMMHRMMWQ4SENSNUQiLCJtYWMiOiI0NzQwMGZhM2FiNmFmYjAyYjY0OTBlNTA1NzgyNDgwMjM2NDMwY2Y1MDEzZmM2N2QwNDY5YmVjY2U5ZWQyZmNjIiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6InBnRFIrTFh3TlpVNnNCS3hlelYwNVE9PSIsInZhbHVlIjoiK0tES0hCU2p0am5WRGVDbi84WGdsYnBObmM0MThRRVNZVXVCQ2FoUzdjRWw4eTNYNGNlZVlzYzZMZU80Y2d6MEdOMzRtZkhqVWlsQ3VNOG1Nam44cXd0RkFjSlJabGwxQ0dZanpLV3MyRDBwVk5FeTEwdndVaGtkY084RXdLcTgiLCJtYWMiOiI3ODVkNTIwMjA4ZmYyMzRiYjc4NDAyYjQ1YWJkY2QxOTJhODMwMTNkNjAzM2I4OTYxOTYwMTI0YjZmYWYyMjc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192744078\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-175692520 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">8|uEm0mlZkc74gIicIsEcLEvV0115i1N3Y0ZTpeVcevaERH48GCO4oF8pIiIWH|$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJVQrpdUzynGiTnyeG8i2DIDXWmXKGLYbxNp8SYN</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8YoafvmOCmZQ4H6nX6KaFPN9SGGWh5eg9fX9VD1Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175692520\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-159829047 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 16:59:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRiakEvMkYyNXUra2ZIVFluazZaTUE9PSIsInZhbHVlIjoidnJTK3g2VGJrQ0Y1SHg3M2FqU0JoOFBDOVJrOHg2RVhUdEc0bWhWSVRIU01qTWd4b3grYXpGUGdJQUNkZ3JiQVRTdVhicFVaVjlGOGoyWkRRNk1LMGhJbFZnWU5uZ0xob05hUE05dXUxNG0vbnA1dTE4dk9adUUyNTRiZjB0bWkiLCJtYWMiOiJlMDJhZjM4YjdiMTJlNTQyNjY3NDcxNWYxOWVmNDIzMGJkNzIxNWU4NjJjZWZkZWY5NDI3MmExYmMwMmYyNTMyIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 18:59:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6InNZNkpiLzdvQWc2dlJzODNVQlBWdkE9PSIsInZhbHVlIjoialFmaDZMQ0c1OUNzS1VLR2lyakFPcGNyZnZvalFTeXdLY0tFdzJuMzcxVUNRWEZIcTNGdVpTa2t6TytnMmg5Ym85aG5xTWs1T3cvL2YrVldDa1h5M0ZONVYxb2VZVXRxTlhpSjFZLytxeDR2c3ZIM1owVWlDUW53TEFrRGhFRU0iLCJtYWMiOiI1NTdhY2UwODZiYTgxMzQzMTNjOTFhZmFkMWRlYjkxODU0YzFjODY2NTU0MDFjYjhmODgyOTYwNDEzZmRjMTk4IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 18:59:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRiakEvMkYyNXUra2ZIVFluazZaTUE9PSIsInZhbHVlIjoidnJTK3g2VGJrQ0Y1SHg3M2FqU0JoOFBDOVJrOHg2RVhUdEc0bWhWSVRIU01qTWd4b3grYXpGUGdJQUNkZ3JiQVRTdVhicFVaVjlGOGoyWkRRNk1LMGhJbFZnWU5uZ0xob05hUE05dXUxNG0vbnA1dTE4dk9adUUyNTRiZjB0bWkiLCJtYWMiOiJlMDJhZjM4YjdiMTJlNTQyNjY3NDcxNWYxOWVmNDIzMGJkNzIxNWU4NjJjZWZkZWY5NDI3MmExYmMwMmYyNTMyIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 18:59:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6InNZNkpiLzdvQWc2dlJzODNVQlBWdkE9PSIsInZhbHVlIjoialFmaDZMQ0c1OUNzS1VLR2lyakFPcGNyZnZvalFTeXdLY0tFdzJuMzcxVUNRWEZIcTNGdVpTa2t6TytnMmg5Ym85aG5xTWs1T3cvL2YrVldDa1h5M0ZONVYxb2VZVXRxTlhpSjFZLytxeDR2c3ZIM1owVWlDUW53TEFrRGhFRU0iLCJtYWMiOiI1NTdhY2UwODZiYTgxMzQzMTNjOTFhZmFkMWRlYjkxODU0YzFjODY2NTU0MDFjYjhmODgyOTYwNDEzZmRjMTk4IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 18:59:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159829047\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1173498000 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJVQrpdUzynGiTnyeG8i2DIDXWmXKGLYbxNp8SYN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>Dashboard_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>businessCustomersOnly</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173498000\", {\"maxDepth\":0})</script>\n"}}
{"__meta": {"id": "X379777ab2d3cee1b87c2f272b0b4920b", "datetime": "2025-06-22 03:49:39", "utime": **********.642527, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750564178.555805, "end": **********.642558, "duration": 1.0867531299591064, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1750564178.555805, "relative_start": 0, "end": 1750564178.911971, "relative_end": 1750564178.911971, "duration": 0.35616612434387207, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750564178.911979, "relative_start": 0.3561739921569824, "end": **********.64256, "relative_end": 1.9073486328125e-06, "duration": 0.7305810451507568, "duration_str": "731ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52317408, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable@unmountAction", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=401\" onclick=\"\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:401-444</a>"}, "queries": {"nb_statements": 20, "nb_visible_statements": 20, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.007350000000000001, "accumulated_duration_str": "7.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.405637, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 7.347}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4094791, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 7.347, "width_percent": 4.49}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 308}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}], "start": **********.4159062, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "User.php:187", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=187", "ajax": false, "filename": "User.php", "line": "187"}, "connection": "edu_db2", "explain": null, "start_percent": 11.837, "width_percent": 6.395}, {"sql": "select * from `tasks` where `tasks`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.4235418, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "edu_db2", "explain": null, "start_percent": 18.231, "width_percent": 5.85}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4560559, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 24.082, "width_percent": 8.163}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.459275, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 32.245, "width_percent": 3.129}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.461365, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 35.374, "width_percent": 4.626}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.463902, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 40, "width_percent": 5.306}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4660819, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 45.306, "width_percent": 3.537}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.468537, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 48.844, "width_percent": 4.218}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.470773, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 53.061, "width_percent": 3.265}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4729042, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 56.327, "width_percent": 3.81}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.4829109, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 60.136, "width_percent": 8.027}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.4854078, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 68.163, "width_percent": 3.129}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.487099, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 71.293, "width_percent": 3.265}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.489363, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 74.558, "width_percent": 8.027}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.4918401, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 82.585, "width_percent": 4.218}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.493734, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 86.803, "width_percent": 3.129}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.495711, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 89.932, "width_percent": 5.034}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.497796, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 94.966, "width_percent": 5.034}]}, "models": {"data": {"App\\Models\\TeachingSchedule": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeachingSchedule.php&line=1", "ajax": false, "filename": "TeachingSchedule.php", "line": "?"}}, "App\\Models\\Task": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTask.php&line=1", "ajax": false, "filename": "Task.php", "line": "?"}}, "App\\Models\\Lesson": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FLesson.php&line=1", "ajax": false, "filename": "Lesson.php", "line": "?"}}, "App\\Models\\User": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ClassRoom": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FClassRoom.php&line=1", "ajax": false, "filename": "ClassRoom.php", "line": "?"}}, "App\\Models\\Subject": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FSubject.php&line=1", "ajax": false, "filename": "Subject.php", "line": "?"}}, "App\\Models\\Team": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 128, "is_counter": true}, "livewire": {"data": {"app.filament.resources.task-resource.pages.task-timetable #1Ebh7zy8339PePEbBRkS": "array:4 [\n  \"data\" => array:20 [\n    \"selectedDate\" => \"2025-06-22\"\n    \"viewMode\" => \"week\"\n    \"editingTask\" => App\\Models\\Task {#3651\n      #connection: \"mysql\"\n      #table: \"tasks\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:31 [\n        \"id\" => 14\n        \"team_id\" => 1\n        \"user_id\" => 7\n        \"assigned_to\" => 7\n        \"title\" => \"Sprint Planning\"\n        \"description\" => \"Plan tasks for the upcoming sprint\"\n        \"start_datetime\" => \"2025-06-22 10:00:04\"\n        \"end_datetime\" => \"2025-06-22 11:30:04\"\n        \"priority\" => \"high\"\n        \"status\" => \"completed\"\n        \"color\" => \"#F59E0B\"\n        \"has_alert\" => 1\n        \"alert_datetime\" => null\n        \"alert_minutes_before\" => 15\n        \"is_recurring\" => 0\n        \"recurring_pattern\" => null\n        \"recurring_end_date\" => null\n        \"alert_sound\" => 1\n        \"alert_email\" => 0\n        \"alert_line\" => 0\n        \"line_user_id\" => null\n        \"position_x\" => null\n        \"position_y\" => null\n        \"width\" => null\n        \"height\" => null\n        \"metadata\" => null\n        \"is_all_day\" => 0\n        \"location\" => null\n        \"notes\" => null\n        \"created_at\" => \"2025-06-21 08:31:05\"\n        \"updated_at\" => \"2025-06-22 03:48:17\"\n      ]\n      #original: array:31 [\n        \"id\" => 14\n        \"team_id\" => 1\n        \"user_id\" => 7\n        \"assigned_to\" => 7\n        \"title\" => \"Sprint Planning\"\n        \"description\" => \"Plan tasks for the upcoming sprint\"\n        \"start_datetime\" => \"2025-06-22 10:00:04\"\n        \"end_datetime\" => \"2025-06-22 11:30:04\"\n        \"priority\" => \"high\"\n        \"status\" => \"completed\"\n        \"color\" => \"#F59E0B\"\n        \"has_alert\" => 1\n        \"alert_datetime\" => null\n        \"alert_minutes_before\" => 15\n        \"is_recurring\" => 0\n        \"recurring_pattern\" => null\n        \"recurring_end_date\" => null\n        \"alert_sound\" => 1\n        \"alert_email\" => 0\n        \"alert_line\" => 0\n        \"line_user_id\" => null\n        \"position_x\" => null\n        \"position_y\" => null\n        \"width\" => null\n        \"height\" => null\n        \"metadata\" => null\n        \"is_all_day\" => 0\n        \"location\" => null\n        \"notes\" => null\n        \"created_at\" => \"2025-06-21 08:31:05\"\n        \"updated_at\" => \"2025-06-22 03:48:17\"\n      ]\n      #changes: []\n      #casts: array:17 [\n        \"start_datetime\" => \"datetime\"\n        \"end_datetime\" => \"datetime\"\n        \"alert_datetime\" => \"datetime\"\n        \"recurring_end_date\" => \"date\"\n        \"has_alert\" => \"boolean\"\n        \"is_recurring\" => \"boolean\"\n        \"alert_sound\" => \"boolean\"\n        \"alert_email\" => \"boolean\"\n        \"alert_line\" => \"boolean\"\n        \"is_all_day\" => \"boolean\"\n        \"recurring_pattern\" => \"array\"\n        \"metadata\" => \"array\"\n        \"position_x\" => \"integer\"\n        \"position_y\" => \"integer\"\n        \"width\" => \"integer\"\n        \"height\" => \"integer\"\n        \"alert_minutes_before\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:28 [\n        0 => \"team_id\"\n        1 => \"user_id\"\n        2 => \"assigned_to\"\n        3 => \"title\"\n        4 => \"description\"\n        5 => \"start_datetime\"\n        6 => \"end_datetime\"\n        7 => \"priority\"\n        8 => \"status\"\n        9 => \"color\"\n        10 => \"has_alert\"\n        11 => \"alert_datetime\"\n        12 => \"alert_minutes_before\"\n        13 => \"is_recurring\"\n        14 => \"recurring_pattern\"\n        15 => \"recurring_end_date\"\n        16 => \"alert_sound\"\n        17 => \"alert_email\"\n        18 => \"alert_line\"\n        19 => \"line_user_id\"\n        20 => \"position_x\"\n        21 => \"position_y\"\n        22 => \"width\"\n        23 => \"height\"\n        24 => \"metadata\"\n        25 => \"is_all_day\"\n        26 => \"location\"\n        27 => \"notes\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"editingSchedule\" => null\n    \"editTaskData\" => array:10 [\n      \"title\" => \"Sprint Planning\"\n      \"description\" => \"Plan tasks for the upcoming sprint\"\n      \"start_datetime\" => Illuminate\\Support\\Carbon @1750586404 {#3650\n        #endOfTime: false\n        #startOfTime: false\n        #constructedObjectId: \"0000000000000e420000000000000000\"\n        -clock: null\n        #localMonthsOverflow: null\n        #localYearsOverflow: null\n        #localStrictModeEnabled: null\n        #localHumanDiffOptions: null\n        #localToStringFormat: null\n        #localSerializer: null\n        #localMacros: null\n        #localGenericMacros: null\n        #localFormatFunction: null\n        #localTranslator: null\n        #dumpProperties: array:3 [\n          0 => \"date\"\n          1 => \"timezone_type\"\n          2 => \"timezone\"\n        ]\n        #dumpLocale: null\n        #dumpDateProperties: null\n        date: 2025-06-22 10:00:04.0 +00:00\n      }\n      \"end_datetime\" => Illuminate\\Support\\Carbon @1750591804 {#3562\n        #endOfTime: false\n        #startOfTime: false\n        #constructedObjectId: \"0000000000000dea0000000000000000\"\n        -clock: null\n        #localMonthsOverflow: null\n        #localYearsOverflow: null\n        #localStrictModeEnabled: null\n        #localHumanDiffOptions: null\n        #localToStringFormat: null\n        #localSerializer: null\n        #localMacros: null\n        #localGenericMacros: null\n        #localFormatFunction: null\n        #localTranslator: null\n        #dumpProperties: array:3 [\n          0 => \"date\"\n          1 => \"timezone_type\"\n          2 => \"timezone\"\n        ]\n        #dumpLocale: null\n        #dumpDateProperties: null\n        date: 2025-06-22 11:30:04.0 +00:00\n      }\n      \"priority\" => \"high\"\n      \"status\" => \"completed\"\n      \"color\" => \"#F59E0B\"\n      \"location\" => null\n      \"has_alert\" => true\n      \"assigned_to\" => 7\n    ]\n    \"editScheduleData\" => []\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => []\n    \"defaultActionArguments\" => []\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.resources.task-resource.pages.task-timetable\"\n  \"component\" => \"App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable\"\n  \"id\" => \"1Ebh7zy8339PePEbBRkS\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/backend/default-team/tasks\"\n]", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-975202668 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-975202668\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-237857574 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-237857574\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1889167734 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1774 characters\">{&quot;data&quot;:{&quot;selectedDate&quot;:&quot;2025-06-22&quot;,&quot;viewMode&quot;:&quot;week&quot;,&quot;editingTask&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Task&quot;,&quot;key&quot;:14,&quot;s&quot;:&quot;mdl&quot;}],&quot;editingSchedule&quot;:null,&quot;editTaskData&quot;:[{&quot;title&quot;:&quot;Sprint Planning&quot;,&quot;description&quot;:&quot;Plan tasks for the upcoming sprint&quot;,&quot;start_datetime&quot;:[&quot;2025-06-22T10:00:04+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;end_datetime&quot;:[&quot;2025-06-22T11:30:04+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;priority&quot;:&quot;high&quot;,&quot;status&quot;:&quot;completed&quot;,&quot;color&quot;:&quot;#F59E0B&quot;,&quot;location&quot;:null,&quot;has_alert&quot;:true,&quot;assigned_to&quot;:7},{&quot;s&quot;:&quot;arr&quot;}],&quot;editScheduleData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[&quot;edit_task&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[[[],{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[[{&quot;title&quot;:&quot;Sprint Planning&quot;,&quot;description&quot;:&quot;Plan tasks for the upcoming sprint&quot;,&quot;start_datetime&quot;:&quot;2025-06-22 10:00&quot;,&quot;end_datetime&quot;:&quot;2025-06-22 11:30&quot;,&quot;priority&quot;:&quot;high&quot;,&quot;status&quot;:&quot;completed&quot;,&quot;color&quot;:&quot;#F59E0B&quot;,&quot;location&quot;:null,&quot;has_alert&quot;:true,&quot;assigned_to&quot;:7},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;1Ebh7zy8339PePEbBRkS&quot;,&quot;name&quot;:&quot;app.filament.resources.task-resource.pages.task-timetable&quot;,&quot;path&quot;:&quot;backend\\/default-team\\/tasks&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;2267275049-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;e80dcf72cbc442959777d1b487bdb8d77123a01a8fafec3f107f1d5a968b4d7a&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">unmountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-const>false</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-const>false</span>\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889167734\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-557421966 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2189</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1311 characters\">appearance=system; __next_hmr_refresh_hash__=333; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjF6REVKTlB6UVRDWlVQa1lGVHBWaWc9PSIsInZhbHVlIjoiNEoreDBGNjVwU3JpQTRPYzNwdG9vOFNrSGR6TmZDM0FBTVVjWXJ5c2dYY2xXdTJzOEdyWm0zK1ZJL3llL0xJNGxNRXU0RlFaRDZYSEszaU5IMlMwVFBaK1VnYWNqYUJyMSsrU3U2QUlPc2VySndjeHpTeDRabGFpaERralEwM3pZUkZ3TE1JUHYyejlpdFpaZTk5Wk5ZNVF0cEpJVDljN2daWm8wZ0hIaHphL053WS9Ndm9PK3l0K09wVDQxTStGV2JOeC9SMC9QM3d4MTAzS25VYmszZjJSUjhXWEVjNzVLc3FyNFFZRDVXbz0iLCJtYWMiOiIyYjgyOWMxMzVjYmM2MjRiZjk5MmUyYWM2Yjc4ZTUxMDkxZDkzODQzZDM5ZDMyY2RiNDlkMmVmMWFjMzdkMTMxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlVsV00xODRiRTd6ZHJWbEsxb1ZQc3c9PSIsInZhbHVlIjoiQ0lqUkIrVis1YUg4cnFPNDVTWG9kNW5oc3BzdUZVQTBsMnBJWDdVbTlJYmJXam5lVG1PeExzZTZualB4bVJObVhTV1ZMcEJqVEMxWlN4QmFxZ0xSdjVuUnZTZXhtalV0cmZFOGliUlFTL25zcU4yWWh1bUhTRlRXaWxWc1ZxdXciLCJtYWMiOiIyYjNhMjNiM2IwMjQ2MDUzZmMxMzU1MTVjMjc3N2RjZTU3YWE3MDFkZDE0MjdkNjFhZGE1NDU5MTEzMWJlMTRjIiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IkxJY1FRNThLb0RUUVFNME5nWnRQdnc9PSIsInZhbHVlIjoiNkcyVFBQUkJtaTBFZGJqWFhydUlFb2lEZGg4UVhXRkI1OGFsVThwZmsrYnpIVWJmRFJ6MEQvQ1FUWFYwSGhkSnRmK0hucy9hTnFRWEdiaUxUSFhIbDYxQmc2cDRhUHlNaW4yMnFVMmFGS2NiV2lrWU8wYUhiYzQyaXkxU3BLL0ciLCJtYWMiOiIwZTdhOTUwMTdmNjdlZTMzNzNjZDk3ZWMyOGQ0NjVhNGQ3MzczNTllNDVhOTRkMjQ5YzdmMjk2NzQ2ZjNlZGVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557421966\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|auVELv1y9Y5lanlgXrSzl83b2PJDR2un8lZsqSpPB8SBoxnjyQzLhkluxh9g|$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8giMLlCM1a53blApVUs4YhDapzkhLDegMd42STu6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1229636369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 03:49:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikkva2ZuKzhsNjhNaUhEa3lUeHA5TUE9PSIsInZhbHVlIjoianBHNXBHaHdBblBYZm9lKzhtTDJuc25FQjVONkJGT2dEYmttZkZIaDVISGV3Tm5KblhzOUNQK1Y1aXdjUVhmeFd6QzhQZStiUWpVOHErRjg0SDlFSDc3MUp3UVJWV29ETnZjZDVHRzU0MGk5aTdoSGswc0RUaU43TjFxdEJXemMiLCJtYWMiOiIzNWViNWNiNzBiODRkOGQ4N2U2YWU3MWU1Nzc0MDI4MDk1ZjY2NzRiOGYwODg2YWFkYzhmYzRiOGVmNjU3N2MxIiwidGFnIjoiIn0%3D; expires=Sun, 22 Jun 2025 05:49:39 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IklNcnVZQkwvcjlZTWtsUzZwK1JOcGc9PSIsInZhbHVlIjoidjhmanpjbkgzOVNZbitSOEFHaXkxbmFVQ1h5NVo0WitOTVFQNTYvSW9TTXVLTmdud3BsK3VmaW9wUjdqczZKeWRjSmxxT0ltYjZ0Wi9VSklCYmRqVHRldmFncEZKcmFzYm5UakMwREJvR0FEQWxuOUFnRDNPandQZTNNdlY0b0IiLCJtYWMiOiI1ZWNiYjE4MjNhYjAzZTk3N2ZkYjE4MmIxODEyODU3Y2VmMTY3YTBhNDMzMjQ1ZjgzNDcyZWEwODA4MjY4ODJhIiwidGFnIjoiIn0%3D; expires=Sun, 22 Jun 2025 05:49:39 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikkva2ZuKzhsNjhNaUhEa3lUeHA5TUE9PSIsInZhbHVlIjoianBHNXBHaHdBblBYZm9lKzhtTDJuc25FQjVONkJGT2dEYmttZkZIaDVISGV3Tm5KblhzOUNQK1Y1aXdjUVhmeFd6QzhQZStiUWpVOHErRjg0SDlFSDc3MUp3UVJWV29ETnZjZDVHRzU0MGk5aTdoSGswc0RUaU43TjFxdEJXemMiLCJtYWMiOiIzNWViNWNiNzBiODRkOGQ4N2U2YWU3MWU1Nzc0MDI4MDk1ZjY2NzRiOGYwODg2YWFkYzhmYzRiOGVmNjU3N2MxIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 05:49:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IklNcnVZQkwvcjlZTWtsUzZwK1JOcGc9PSIsInZhbHVlIjoidjhmanpjbkgzOVNZbitSOEFHaXkxbmFVQ1h5NVo0WitOTVFQNTYvSW9TTXVLTmdud3BsK3VmaW9wUjdqczZKeWRjSmxxT0ltYjZ0Wi9VSklCYmRqVHRldmFncEZKcmFzYm5UakMwREJvR0FEQWxuOUFnRDNPandQZTNNdlY0b0IiLCJtYWMiOiI1ZWNiYjE4MjNhYjAzZTk3N2ZkYjE4MmIxODEyODU3Y2VmMTY3YTBhNDMzMjQ1ZjgzNDcyZWEwODA4MjY4ODJhIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 05:49:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229636369\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-596080526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Be2myqdvqkh4AmlPxFV2w2BOlZEFMrcPVbjvr50n</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-596080526\", {\"maxDepth\":0})</script>\n"}}
{"__meta": {"id": "Xc9b72d05ad979e72709cd5a5fdc36b07", "datetime": "2025-06-21 17:48:49", "utime": 1750528129.228582, "method": "GET", "uri": "/backend/default-team/tasks", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750528127.701634, "end": 1750528129.228606, "duration": 1.5269720554351807, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1750528127.701634, "relative_start": 0, "end": **********.161664, "relative_end": **********.161664, "duration": 0.46003007888793945, "duration_str": "460ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.161673, "relative_start": 0.4600391387939453, "end": 1750528129.228608, "relative_end": 1.9073486328125e-06, "duration": 1.0669348239898682, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET backend/{tenant}/tasks", "domain": null, "middleware": "panel:backend, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\HandleSuperAdminAccess, Filament\\Http\\Middleware\\IdentifyTenant, App\\Http\\Middleware\\EnforceTenantAccess, BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant, App\\Http\\Middleware\\RestrictTenantPermissions", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable@__invoke", "as": "filament.backend.resources.tasks.index", "namespace": null, "prefix": "backend/{tenant:slug}/tasks", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.012629999999999999, "accumulated_duration_str": "12.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6918511, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 3.721}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 2 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.70114, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "User.php:187", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=187", "ajax": false, "filename": "User.php", "line": "187"}, "connection": "edu_db2", "explain": null, "start_percent": 3.721, "width_percent": 4.592}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.704967, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 8.314, "width_percent": 2.534}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.740069, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 10.847, "width_percent": 4.671}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.744185, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 15.519, "width_percent": 2.059}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.746075, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 17.577, "width_percent": 1.821}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.748401, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 19.398, "width_percent": 3.325}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.750978, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 22.724, "width_percent": 2.93}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.7535648, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 25.653, "width_percent": 3.167}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.756269, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 28.82, "width_percent": 2.85}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.758651, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 31.671, "width_percent": 2.375}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.769718, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 34.046, "width_percent": 4.751}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7724378, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 38.797, "width_percent": 1.979}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.774357, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 40.776, "width_percent": 1.425}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.776035, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 42.201, "width_percent": 3.009}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7781599, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 45.21, "width_percent": 1.979}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.779883, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 47.189, "width_percent": 1.504}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.781612, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 48.694, "width_percent": 1.425}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.783245, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 50.119, "width_percent": 2.534}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.0984402, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 52.652, "width_percent": 3.484}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": ["new", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.103222, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 56.136, "width_percent": 10.214}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.107719, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 66.35, "width_percent": 1.742}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasRoutes.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 199}], "start": 1750528129.1324089, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 68.092, "width_percent": 3.088}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.136501, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 71.18, "width_percent": 1.504}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.138497, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 72.684, "width_percent": 2.454}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.141205, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 75.139, "width_percent": 1.663}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": "view", "name": "filament-panels::components.tenant-menu", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/vendor/filament-panels/components/tenant-menu.blade.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1750528129.146926, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 76.801, "width_percent": 3.009}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.15212, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 79.81, "width_percent": 2.296}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.1544611, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 82.106, "width_percent": 2.613}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.157095, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 84.719, "width_percent": 1.346}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.160796, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 86.065, "width_percent": 1.504}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.162843, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 87.569, "width_percent": 1.663}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.1652179, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 89.232, "width_percent": 1.425}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.169813, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 90.657, "width_percent": 1.584}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.1718962, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 92.241, "width_percent": 2.138}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.17449, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 94.378, "width_percent": 1.425}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.1783, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 95.804, "width_percent": 1.267}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.180158, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 97.07, "width_percent": 1.584}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1750528129.182541, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 98.654, "width_percent": 1.346}]}, "models": {"data": {"App\\Models\\TeachingSchedule": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeachingSchedule.php&line=1", "ajax": false, "filename": "TeachingSchedule.php", "line": "?"}}, "App\\Models\\Task": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTask.php&line=1", "ajax": false, "filename": "Task.php", "line": "?"}}, "App\\Models\\Lesson": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FLesson.php&line=1", "ajax": false, "filename": "Lesson.php", "line": "?"}}, "App\\Models\\User": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ClassRoom": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FClassRoom.php&line=1", "ajax": false, "filename": "ClassRoom.php", "line": "?"}}, "App\\Models\\Subject": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FSubject.php&line=1", "ajax": false, "filename": "Subject.php", "line": "?"}}, "App\\Models\\Team": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 139, "is_counter": true}, "livewire": {"data": {"app.filament.resources.task-resource.pages.task-timetable #NPIigWiD7G0ZFY1dS6rr": "array:4 [\n  \"data\" => array:20 [\n    \"selectedDate\" => \"2025-06-21\"\n    \"viewMode\" => \"week\"\n    \"editingTask\" => null\n    \"editingSchedule\" => null\n    \"editTaskData\" => []\n    \"editScheduleData\" => []\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.resources.task-resource.pages.task-timetable\"\n  \"component\" => \"App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable\"\n  \"id\" => \"NPIigWiD7G0ZFY1dS6rr\"\n]", "filament.livewire.global-search #r5QFgYmKuuLs1IqABXI2": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"r5QFgYmKuuLs1IqABXI2\"\n]", "filament.livewire.notifications #U4tj2dcOPgHgIIG4CUVA": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2913\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"U4tj2dcOPgHgIIG4CUVA\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 43, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-862118466 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862118466\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.714065, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1535893709 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1535893709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.09494, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1365223115 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365223115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.095491, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-881190678 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881190678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.096089, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1261160262 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261160262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.106568, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1365347664 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365347664\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.106888, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1918682994 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918682994\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.107424, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-954324587 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954324587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.109614, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1250868812 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250868812\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.135551, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1543298585 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543298585\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.135774, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1011614398 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011614398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.135993, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-310595927 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310595927\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.140452, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-223488616 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223488616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.140728, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-656722128 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656722128\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.140945, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1076673107 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076673107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.142891, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-217453770 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217453770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.151135, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-34154773 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34154773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.151357, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1875307864 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875307864\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.151564, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-784031972 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784031972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.156385, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2060140275 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060140275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.156643, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1131561583 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131561583\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.156853, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1733023440 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733023440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.158645, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-965064420 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965064420\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.15989, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-477916660 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-477916660\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.160092, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1433920334 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433920334\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.160294, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-980518575 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980518575\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.164518, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-957833113 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-957833113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.164756, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2122393080 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122393080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.164977, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1123902650 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123902650\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.166836, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-381467103 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381467103\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.168862, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1324546009 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324546009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.169082, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-644919898 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644919898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.169281, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-514818233 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514818233\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.173798, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-732043538 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732043538\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.174046, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-658965151 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658965151\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.174256, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1752295232 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752295232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.176211, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-875128996 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875128996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.17741, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-283568901 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283568901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.177613, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-61161809 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61161809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.177813, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-304460146 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-304460146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.181832, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1355292522 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355292522\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.182099, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1438943173 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438943173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.182303, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-854070694 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854070694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1750528129.184084, "xdebug_link": null}]}, "session": {"_token": "HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "password_hash_web": "$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/default-team/tasks\"\n]"}, "request": {"path_info": "/backend/default-team/tasks", "status_code": "<pre class=sf-dump id=sf-dump-1689011106 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1689011106\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-649669455 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-649669455\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1494067275 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1494067275\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-344210590 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlhMVXJOM3UxdElLRmdvZEo0QWg1WVE9PSIsInZhbHVlIjoiNzVhV3pab29VMDZVRWducTY1aHQzbDV5UzZLQ2hWd1ZmWldtWjFlSWY0V0hkNEpuYTZjSVhVL0pCYmNUTngvZmRCb0pUOTZlM0thT1VScWdCbUxydU9oeUlWbnpVTS8yeHd4U2VzWVhOclczYUE4WEhMWGdCRHFqVEkxVWlkVklsdE9JL2t1bnRVQjlXMHF2N2pzTlRJbi9JVGJQdk42U1hBU0xIcnUzZVN1TnhDL1pmU01mTUsyZndTOGtMYjJpWG5qNkY4WnNkUFhuRDlEejRrL0l5QjNWZEpLc3lFdXA3Sm5hVEJPWHFRWT0iLCJtYWMiOiI1YWU3ODA0MTJjNGZhOTI5NzE0YTFkNmI3NzA2MDYwZWY4ZmUxMjI3ZDMwYjA1MDA3MWU5Y2NlMGI5OTFmMzRmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFVNlpxZlY3aXFtcUFvQ0Fva3FXYnc9PSIsInZhbHVlIjoiU1drbmR2UWQwZ3JSNnFsZTEwNXQ1YkFkWDhRamdFR0doeUNqUzY4dTRLYitEeWhGVGtnQUhiM3ZjVFU2UDF3RkpXT29vbElNTVV3d3pzOVVjKzU0dDFiT1RoR2FIYkVpMXdNV3FGb2ZwclBWekxLT3ZBR0xzeHpXV2FsSVRHT04iLCJtYWMiOiI4NmM1YjA3NmIyMmVhOGM0MjMyYzNlZmNkZjIwNjE2YThkZGQ1ZGNlZDMyYTU3M2YwZGJjNGQ4NWJkNmJlNzU1IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6Ii8rakthNWcvdEFtZXJkcFBBdms2UVE9PSIsInZhbHVlIjoianJTbWk4VVN5cDBNY2lBZ1Y1ZkhXMTYvOVFuNXRzVTlZR0M1MDdIWEVjbmMyd0E3WjBpNjNzcFlSRHpwdzFVUFJWRDRBWEhBeU1mcHhCNnovQksxZHFseVVxV2hEYTEvVHBwcUFnTWdINVVtQjh3M2lpSnlJUlQwcDBaS3dkYWYiLCJtYWMiOiIwN2JhYjlhYTMyYzBmZWZiYjc2OTE0ZmVmMjg4ZTNjNWY1ODIxYjY4YWZmNWE0ZTQyMjJhMTM5M2JiYTNjZTRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344210590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1243271781 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|ehyf0vWTi0nhH6BCaRuiRLr53ca7SdmqrBrbPg7dO3Vyvr6mhT3JSPnhkcqE|$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vsL2QWIgJub4i1B2dsBCWSF9PlW0AmpHCXRUUDpe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243271781\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-363773865 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:48:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ilo5NDBzRlg0WFI1ZFZRZlE1MlpYSGc9PSIsInZhbHVlIjoiYlJRVGZzUDBRTWJ0Skc3WmtVODNWd00vcHlPOTdQaXM3OWtlUXo2NnBUUEU2dGI4TVBoZUdtdUR2cDZFc3Z2MW1jSk1Lb2w4VHc0VFoyV2JEVENoaFFqUnBCSnBlcEVzd3VMZkN0RWxzMi9mT1ZTTm53NFVTaWpPVUUybUFsMTQiLCJtYWMiOiI5ZTcwMzE3ZDdkYzdjY2MxNjFjOGE2MzljMWFhNGI3OTM0MTIzMWIwNDkzODgzZGQyZTg0YzIyNWQ0ODg5YjE1IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:48:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6ImM1SVJCN1JiQmFMT1lleTFQenpmWGc9PSIsInZhbHVlIjoieGp3UWo3U0pZTEtJQzNESE80NEpuYjZpdHBpMzhyVW1DV0xwRm5NMzArdmZPdTlNeit3MXRvNStyZWhieUVkSDJIZlN3S0tyTVNSdkJKNDBLN2M5K1lyOTVxU3ZoMXF2VjNUcHk1WVhpRFpFZUVnSDhrUEVLY3VjN3FDekJXY3EiLCJtYWMiOiJjMDhhNjlmYWRhMzVmMGYwNDRjNzkwZjA0OTk2OGMyMmZmOTA4ODdhZDU5OGIxOGNkYzMyYjFmMzRiYjBjNjI3IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:48:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ilo5NDBzRlg0WFI1ZFZRZlE1MlpYSGc9PSIsInZhbHVlIjoiYlJRVGZzUDBRTWJ0Skc3WmtVODNWd00vcHlPOTdQaXM3OWtlUXo2NnBUUEU2dGI4TVBoZUdtdUR2cDZFc3Z2MW1jSk1Lb2w4VHc0VFoyV2JEVENoaFFqUnBCSnBlcEVzd3VMZkN0RWxzMi9mT1ZTTm53NFVTaWpPVUUybUFsMTQiLCJtYWMiOiI5ZTcwMzE3ZDdkYzdjY2MxNjFjOGE2MzljMWFhNGI3OTM0MTIzMWIwNDkzODgzZGQyZTg0YzIyNWQ0ODg5YjE1IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:48:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6ImM1SVJCN1JiQmFMT1lleTFQenpmWGc9PSIsInZhbHVlIjoieGp3UWo3U0pZTEtJQzNESE80NEpuYjZpdHBpMzhyVW1DV0xwRm5NMzArdmZPdTlNeit3MXRvNStyZWhieUVkSDJIZlN3S0tyTVNSdkJKNDBLN2M5K1lyOTVxU3ZoMXF2VjNUcHk1WVhpRFpFZUVnSDhrUEVLY3VjN3FDekJXY3EiLCJtYWMiOiJjMDhhNjlmYWRhMzVmMGYwNDRjNzkwZjA0OTk2OGMyMmZmOTA4ODdhZDU5OGIxOGNkYzMyYjFmMzRiYjBjNjI3IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:48:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363773865\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1506749901 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1506749901\", {\"maxDepth\":0})</script>\n"}}
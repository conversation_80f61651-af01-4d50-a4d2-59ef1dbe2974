<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasTeamScopedPolymorphicRelations;

class Book extends Model
{
    use HasFactory;
    use HasTeamScopedPolymorphicRelations;

    protected $fillable = [
        'team_id',
        'subject_id',
        'title',
        'author',
        'description',
        'isbn',
        'publisher',
        'published_date',
        'edition',
        'pages',
        'language',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'published_date' => 'date',
        'pages' => 'integer',
    ];

    /**
     * Get the team that owns the book
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the subject that the book belongs to
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the lessons for this book
     */
    public function lessons()
    {
        return $this->belongsToMany(Lesson::class, 'lessonables', 'lessonable_id', 'lesson_id')
            ->wherePivot('lessonable_type', static::class)
            ->withPivot(['team_id', 'sort_order', 'lessonable_type'])
            ->withTimestamps()
            ->orderByPivot('sort_order');
    }

    /**
     * Scope a query to only include active books
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by subject
     */
    public function scopeForSubject(Builder $query, int $subjectId): Builder
    {
        return $query->where('subject_id', $subjectId);
    }

    /**
     * Scope a query to filter by team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Get the display title with author
     */
    public function getDisplayTitleAttribute(): string
    {
        return "{$this->title} by {$this->author}";
    }

    /**
     * Get formatted publication info
     */
    public function getPublicationInfoAttribute(): string
    {
        $info = [];

        if ($this->publisher) {
            $info[] = $this->publisher;
        }

        if ($this->published_date) {
            $info[] = $this->published_date->format('Y');
        }

        if ($this->edition) {
            $info[] = $this->edition . ' Edition';
        }

        return implode(' • ', $info);
    }

    /**
     * Get the lesson count
     */
    public function getLessonCountAttribute(): int
    {
        return $this->lessons()->count();
    }
}

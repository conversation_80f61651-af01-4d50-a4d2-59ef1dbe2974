<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Lesson extends Model
{
    use HasFactory;

    protected $fillable = [
        'book_id',
        'course_id',
        'title',
        'chapter',
        'content',
        'objectives',
        'summary',
        'duration_minutes',
        'sort_order',
        'is_published',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'duration_minutes' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the book that owns the lesson
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the course that owns the lesson
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the subject through the book relationship
     */
    public function subject(): BelongsTo
    {
        return $this->book->subject();
    }

    /**
     * Get the team through the book relationship
     */
    public function team(): BelongsTo
    {
        return $this->book->team();
    }

    /**
     * Get the assignments for this lesson
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * Scope a query to only include published lessons
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope a query to filter by book
     */
    public function scopeForBook(Builder $query, int $bookId): Builder
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope a query to order by sort order
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order', 'asc');
    }

    /**
     * Get the display title with chapter
     */
    public function getDisplayTitleAttribute(): string
    {
        if ($this->chapter) {
            return "{$this->chapter}: {$this->title}";
        }

        return $this->title;
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'Duration not set';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . ' minutes';
    }

    /**
     * Get content preview (first 150 characters)
     */
    public function getContentPreviewAttribute(): string
    {
        return \Str::limit(strip_tags($this->content), 150);
    }

    /**
     * Check if lesson has objectives
     */
    public function hasObjectives(): bool
    {
        return !empty($this->objectives);
    }

    /**
     * Check if lesson has summary
     */
    public function hasSummary(): bool
    {
        return !empty($this->summary);
    }
}

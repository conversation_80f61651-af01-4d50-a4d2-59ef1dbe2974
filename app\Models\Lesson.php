<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasTeamScopedPolymorphicRelations;

class Lesson extends Model
{
    use HasFactory;
    use HasTeamScopedPolymorphicRelations;

    protected $fillable = [
        'title',
        'chapter',
        'content',
        'objectives',
        'summary',
        'duration_minutes',
        'sort_order',
        'is_published',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'duration_minutes' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the books that this lesson belongs to
     */
    public function books()
    {
        return $this->belongsToMany(Book::class, 'lessonables', 'lesson_id', 'lessonable_id')
            ->wherePivot('lessonable_type', Book::class)
            ->withPivot(['team_id', 'sort_order', 'lessonable_type'])
            ->withTimestamps()
            ->orderByPivot('sort_order');
    }

    /**
     * Get the courses that this lesson belongs to
     */
    public function courses()
    {
        return $this->belongsToMany(Course::class, 'lessonables', 'lesson_id', 'lessonable_id')
            ->wherePivot('lessonable_type', Course::class)
            ->withPivot(['team_id', 'sort_order', 'lessonable_type'])
            ->withTimestamps()
            ->orderByPivot('sort_order');
    }

    /**
     * Get the subjects through the books and courses relationships
     */
    public function subjects()
    {
        $bookSubjects = $this->books()->with('subject')->get()->pluck('subject')->filter();
        $courseSubjects = $this->courses()->with('subject')->get()->pluck('subject')->filter();

        return $bookSubjects->merge($courseSubjects)->unique('id');
    }

    /**
     * Get the teams through the books and courses relationships
     */
    public function teams()
    {
        $bookTeams = $this->books()->with('team')->get()->pluck('team')->filter();
        $courseTeams = $this->courses()->with('team')->get()->pluck('team')->filter();

        return $bookTeams->merge($courseTeams)->unique('id');
    }

    /**
     * Get the assignments for this lesson
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * Scope a query to only include published lessons
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope a query to filter by book
     */
    public function scopeForBook(Builder $query, int $bookId): Builder
    {
        return $query->whereHas('books', function ($q) use ($bookId) {
            $q->where('books.id', $bookId);
        });
    }

    /**
     * Scope a query to filter by course
     */
    public function scopeForCourse(Builder $query, int $courseId): Builder
    {
        return $query->whereHas('courses', function ($q) use ($courseId) {
            $q->where('courses.id', $courseId);
        });
    }

    /**
     * Scope a query to order by sort order
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order', 'asc');
    }

    /**
     * Get the display title with chapter
     */
    public function getDisplayTitleAttribute(): string
    {
        if ($this->chapter) {
            return "{$this->chapter}: {$this->title}";
        }

        return $this->title;
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'Duration not set';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . ' minutes';
    }

    /**
     * Get content preview (first 150 characters)
     */
    public function getContentPreviewAttribute(): string
    {
        return \Str::limit(strip_tags($this->content), 150);
    }

    /**
     * Check if lesson has objectives
     */
    public function hasObjectives(): bool
    {
        return !empty($this->objectives);
    }

    /**
     * Check if lesson has summary
     */
    public function hasSummary(): bool
    {
        return !empty($this->summary);
    }
}

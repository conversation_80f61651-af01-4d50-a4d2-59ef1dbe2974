<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssignmentResource\Pages;
use App\Filament\Resources\AssignmentResource\RelationManagers;
use App\Models\Assignment;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AssignmentResource extends Resource
{
    protected static ?string $model = Assignment::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationLabel = 'Assignments';

    protected static ?string $modelLabel = 'Assignment';

    protected static ?string $pluralModelLabel = 'Assignments';

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'assignments';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),

                        Forms\Components\Select::make('subject_id')
                            ->label('Subject')
                            ->relationship('subject', 'name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->where('is_active', true);
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->columnSpan(1),

                        Forms\Components\Select::make('lesson_id')
                            ->label('Lesson (Optional)')
                            ->relationship('lesson', 'title', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->whereHas('book', function (Builder $query) {
                                        $query->where('team_id', Filament::getTenant()->id);
                                    });
                                }
                                return $query->where('is_published', true);
                            })
                            ->searchable()
                            ->preload()
                            ->columnSpan(1),

                        Forms\Components\Select::make('type')
                            ->options([
                                'assignment' => 'Assignment',
                                'quiz' => 'Quiz',
                                'project' => 'Project',
                                'homework' => 'Homework',
                                'exam' => 'Exam',
                                'presentation' => 'Presentation',
                            ])
                            ->default('assignment')
                            ->required()
                            ->columnSpan(1),

                        Forms\Components\TextInput::make('total_score')
                            ->label('Total Score')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(1000)
                            ->step(0.01)
                            ->placeholder('e.g., 100')
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Schedule & Status')
                    ->schema([
                        Forms\Components\DateTimePicker::make('due_date')
                            ->label('Due Date')
                            ->native(false)
                            ->displayFormat('M j, Y g:i A')
                            ->columnSpan(1),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->maxLength(1000)
                            ->placeholder('Brief description of the assignment'),

                        Forms\Components\RichEditor::make('instructions')
                            ->label('Instructions')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'bulletList',
                                'orderedList',
                                'link',
                                'undo',
                                'redo',
                            ])
                            ->placeholder('Detailed instructions for students'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->wrap(),

                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('lesson.title')
                    ->label('Lesson')
                    ->searchable()
                    ->placeholder('No lesson')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 30 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'assignment' => 'gray',
                        'quiz' => 'warning',
                        'project' => 'info',
                        'homework' => 'success',
                        'exam' => 'danger',
                        'presentation' => 'purple',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('teacher.name')
                    ->label('Teacher')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_score')
                    ->label('Total Score')
                    ->numeric()
                    ->sortable()
                    ->placeholder('Not set'),

                Tables\Columns\TextColumn::make('due_date')
                    ->label('Due Date')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->placeholder('No due date')
                    ->color(fn ($record) => $record->is_overdue ? 'danger' : null),

                Tables\Columns\TextColumn::make('submission_count')
                    ->label('Submissions')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('subject_id')
                    ->label('Subject')
                    ->relationship('subject', 'name', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->where('is_active', true);
                    })
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'assignment' => 'Assignment',
                        'quiz' => 'Quiz',
                        'project' => 'Project',
                        'homework' => 'Homework',
                        'exam' => 'Exam',
                        'presentation' => 'Presentation',
                    ]),

                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Teacher')
                    ->relationship('teacher', 'name', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->whereHas('roles', function (Builder $query) {
                            $query->where('name', 'teacher');
                        });
                    })
                    ->searchable()
                    ->preload(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),

                Tables\Filters\Filter::make('due_soon')
                    ->label('Due Soon (7 days)')
                    ->query(fn (Builder $query): Builder => $query->dueSoon()),

                Tables\Filters\Filter::make('overdue')
                    ->label('Overdue')
                    ->query(fn (Builder $query): Builder => $query->overdue()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('due_date', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SubmissionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssignments::route('/'),
            'create' => Pages\CreateAssignment::route('/create'),
            'edit' => Pages\EditAssignment::route('/{record}/edit'),
        ];
    }
}

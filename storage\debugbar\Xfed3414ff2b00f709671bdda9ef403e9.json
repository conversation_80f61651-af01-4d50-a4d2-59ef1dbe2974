{"__meta": {"id": "Xfed3414ff2b00f709671bdda9ef403e9", "datetime": "2025-06-21 17:06:49", "utime": **********.859123, "method": "GET", "uri": "/backend/default-team/tasks", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750525608.382712, "end": **********.859169, "duration": 1.476457118988037, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1750525608.382712, "relative_start": 0, "end": 1750525608.813915, "relative_end": 1750525608.813915, "duration": 0.43120312690734863, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750525608.813931, "relative_start": 0.43121910095214844, "end": **********.859172, "relative_end": 3.0994415283203125e-06, "duration": 1.045241117477417, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET backend/{tenant}/tasks", "domain": null, "middleware": "panel:backend, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\HandleSuperAdminAccess, Filament\\Http\\Middleware\\IdentifyTenant, App\\Http\\Middleware\\EnforceTenantAccess, BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant, App\\Http\\Middleware\\RestrictTenantPermissions", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable@__invoke", "as": "filament.backend.resources.tasks.index", "namespace": null, "prefix": "backend/{tenant:slug}/tasks", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013539999999999998, "accumulated_duration_str": "13.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.234919, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 3.323}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.242495, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "User.php:187", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=187", "ajax": false, "filename": "User.php", "line": "187"}, "connection": "edu_db2", "explain": null, "start_percent": 3.323, "width_percent": 3.471}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2454379, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 6.795, "width_percent": 1.625}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.296497, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 8.419, "width_percent": 4.653}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.301108, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 13.072, "width_percent": 2.585}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3035731, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 15.657, "width_percent": 2.437}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.306606, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 18.095, "width_percent": 3.988}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.309855, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 22.083, "width_percent": 2.806}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.31393, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 24.889, "width_percent": 2.363}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3176458, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 27.253, "width_percent": 3.176}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.32132, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 30.428, "width_percent": 2.511}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.331652, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 32.939, "width_percent": 4.505}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3342268, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 37.445, "width_percent": 1.773}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.335914, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 39.217, "width_percent": 1.477}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3377829, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 40.694, "width_percent": 2.88}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.33995, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 43.575, "width_percent": 1.994}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.341723, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 45.569, "width_percent": 1.699}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3434598, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 47.267, "width_percent": 1.699}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.345321, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 48.966, "width_percent": 2.659}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.702076, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 51.625, "width_percent": 3.471}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": ["new", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.708573, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 55.096, "width_percent": 6.056}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.713551, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 61.152, "width_percent": 2.216}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasRoutes.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 199}], "start": **********.747182, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 63.368, "width_percent": 4.136}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.751575, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 67.504, "width_percent": 2.29}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.754763, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 69.793, "width_percent": 2.733}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.757481, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 72.526, "width_percent": 1.403}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": "view", "name": "filament-panels::components.tenant-menu", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/vendor/filament-panels/components/tenant-menu.blade.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.764061, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 73.929, "width_percent": 2.954}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.770003, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 76.883, "width_percent": 2.142}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7721598, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 79.025, "width_percent": 1.846}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.774719, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 80.871, "width_percent": 1.699}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.778994, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 82.57, "width_percent": 2.068}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7811048, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 84.638, "width_percent": 1.846}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7838962, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 86.484, "width_percent": 1.994}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.788706, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 88.479, "width_percent": 2.142}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7910612, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 90.62, "width_percent": 2.29}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.793656, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 92.91, "width_percent": 1.256}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7976441, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 94.165, "width_percent": 2.29}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.799891, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 96.455, "width_percent": 1.846}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.802361, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 98.301, "width_percent": 1.699}]}, "models": {"data": {"App\\Models\\TeachingSchedule": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeachingSchedule.php&line=1", "ajax": false, "filename": "TeachingSchedule.php", "line": "?"}}, "App\\Models\\Task": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTask.php&line=1", "ajax": false, "filename": "Task.php", "line": "?"}}, "App\\Models\\Lesson": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FLesson.php&line=1", "ajax": false, "filename": "Lesson.php", "line": "?"}}, "App\\Models\\User": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ClassRoom": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FClassRoom.php&line=1", "ajax": false, "filename": "ClassRoom.php", "line": "?"}}, "App\\Models\\Subject": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FSubject.php&line=1", "ajax": false, "filename": "Subject.php", "line": "?"}}, "App\\Models\\Team": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 139, "is_counter": true}, "livewire": {"data": {"app.filament.resources.task-resource.pages.task-timetable #XVOOH6iYMjXrrjEmrWkB": "array:4 [\n  \"data\" => array:20 [\n    \"selectedDate\" => \"2025-06-21\"\n    \"viewMode\" => \"week\"\n    \"editingTask\" => null\n    \"editingSchedule\" => null\n    \"editTaskData\" => []\n    \"editScheduleData\" => []\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.resources.task-resource.pages.task-timetable\"\n  \"component\" => \"App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable\"\n  \"id\" => \"XVOOH6iYMjXrrjEmrWkB\"\n]", "filament.livewire.global-search #xGbOBCPn7tAeodh1viPg": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"xGbOBCPn7tAeodh1viPg\"\n]", "filament.livewire.notifications #oouB4L31uogxKKWRmrF1": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2913\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"oouB4L31uogxKKWRmrF1\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 43, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-29720795 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29720795\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.254776, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1962176637 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962176637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695642, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1077468415 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077468415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.696478, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1300915060 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300915060\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.697287, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1290842736 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290842736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711664, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-536775269 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536775269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712076, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-845752125 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845752125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713045, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1728077789 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728077789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.715852, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1465862224 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465862224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750548, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-841847494 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841847494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750788, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-96816874 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96816874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75101, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2025312970 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025312970\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.756745, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1929564796 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929564796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757012, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1702405432 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702405432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757223, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177331172 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177331172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75905, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1328263500 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328263500\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768978, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-565974611 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565974611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769217, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-379005255 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379005255\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769432, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1538962879 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538962879\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773959, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1608428199 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608428199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774202, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1385327757 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385327757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774424, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-245832396 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-245832396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776724, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2117215809 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117215809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.778057, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-133507807 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133507807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.778262, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-978896449 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-978896449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.778461, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-814328796 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814328796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.783066, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-845468081 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845468081\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.783411, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-436336309 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436336309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.783624, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1820934297 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820934297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.785592, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-901990122 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901990122\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.787675, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-305931274 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305931274\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.787882, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-433912188 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433912188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.788086, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-417288310 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-417288310\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.792954, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1585738005 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585738005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.79321, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-163802360 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163802360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.793416, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1224911968 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224911968\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.795178, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-95681990 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95681990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.796676, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1188444132 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188444132\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.796887, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-793393681 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793393681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.797085, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-415987520 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415987520\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.801646, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-625127792 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-625127792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.801886, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1936232854 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936232854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.802091, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-622527077 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622527077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.804328, "xdebug_link": null}]}, "session": {"_token": "7Itr5Og7b7TMnK2UoYn0oaPyL7yUfGzPcudK4RTo", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/backend/default-team/tasks\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "filament": "[]", "Dashboard_filters": "array:3 [\n  \"businessCustomersOnly\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"path_info": "/backend/default-team/tasks", "status_code": "<pre class=sf-dump id=sf-dump-2141817981 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2141817981\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1979129103 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1979129103\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1367984016 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1367984016\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2063727543 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1311 characters\">appearance=system; __next_hmr_refresh_hash__=333; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjF6REVKTlB6UVRDWlVQa1lGVHBWaWc9PSIsInZhbHVlIjoiNEoreDBGNjVwU3JpQTRPYzNwdG9vOFNrSGR6TmZDM0FBTVVjWXJ5c2dYY2xXdTJzOEdyWm0zK1ZJL3llL0xJNGxNRXU0RlFaRDZYSEszaU5IMlMwVFBaK1VnYWNqYUJyMSsrU3U2QUlPc2VySndjeHpTeDRabGFpaERralEwM3pZUkZ3TE1JUHYyejlpdFpaZTk5Wk5ZNVF0cEpJVDljN2daWm8wZ0hIaHphL053WS9Ndm9PK3l0K09wVDQxTStGV2JOeC9SMC9QM3d4MTAzS25VYmszZjJSUjhXWEVjNzVLc3FyNFFZRDVXbz0iLCJtYWMiOiIyYjgyOWMxMzVjYmM2MjRiZjk5MmUyYWM2Yjc4ZTUxMDkxZDkzODQzZDM5ZDMyY2RiNDlkMmVmMWFjMzdkMTMxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im5EbXpMMjhjL1Urc2ZtZXJmWFNmMGc9PSIsInZhbHVlIjoibEQ4TUVWbCs3Wmx0aVRXcVVNQU1ZRGNsb3hsMGpHTkxRYVpKNnhtaHFhV0JJSjllU2JaTjZHSXVTQnJYNjV0SVZnSlZpR0tIOFAyalVsb1hmbFM3RGtoT3VWTW5uQUhkMWF2M2h2TS93LzJNeExpWHRlQURHZ3RJa1FuQVRCbzUiLCJtYWMiOiIzYzc1OThkNjNhZjg5OGMyMGYwY2U4NzA3YjUxM2ZmODAyNmM0YmQ1OTBhNDI1MWJkNmEwYmQyM2VkYTM4OGU5IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IjJnQW5zMHZCYjVzT2JJNGxibnlNUVE9PSIsInZhbHVlIjoid2NyQlhwUzREVjJXU05pTmN6NUV5U1lxTWUxakpGbHNzaHBodElXeU9EeGxZZVh0UjBpY3YwMlplSGg3VHVIcDdZSFptOXJSVXp6RjA2elBuclJ6SHJTWUU4WnkxS21DWUhyVTcvMWF5WVNSRWY4U3hMNFhUMktiN3FRZzIvNGkiLCJtYWMiOiIyODE2ZDYxYjg0NzRlNmNlYWVmNzM3NWU3YjA0ZTY5N2I2MDExOGE4NmJmZTI1ZDkxYWQyMTc3Y2VjN2FiNzc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063727543\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-722476728 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|auVELv1y9Y5lanlgXrSzl83b2PJDR2un8lZsqSpPB8SBoxnjyQzLhkluxh9g|$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7Itr5Og7b7TMnK2UoYn0oaPyL7yUfGzPcudK4RTo</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ErxHtQIaHBvwCE80n5zfhZC230je0GWumZzyhtZF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722476728\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1627158299 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:06:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik5iNWVsQ1REWStYUmY0dGxrc09ZQ3c9PSIsInZhbHVlIjoiTDArdEQ4cWpmb1FGOTJCTkpzb2E5Vi9SVlRBOVMwQjlmdGlpWUJXb1RmQmhoTW1CcWNOdjlMN1Bwa2Y3TlVqWDIxNmFIM2xUSW53azVPSUxHdElCQ0hQYmV1MHRjb21WaGEwN2R2ZEN3Ky8wWjc0dUZ5VFlUR0p6aExaUlRVU2wiLCJtYWMiOiI0NzlhYWQ1ZThiZjFjZjRlN2U2ZTNmZDk2YTBhMTBmYjZkMWJkZjUwNDhiYWFmMGNlODUzYmI3ZTJlMTg3OGZhIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:06:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6Ik5sV0J6bmc3bmtEd3dvUE03QTZqRXc9PSIsInZhbHVlIjoiZ1B0NHFwRThLdVJNSlhHd0dLRFNtc2J5Vk9vQkNzUllxUUFVdkVlNWtuZXA2TE1FaVQ1VUtoYWdPZC9yOXJBRmxCZkNFQkxiR2hscHlreGpPeEo3cGgzWDU0NEUwNVJaWXQzTWVmSm1xZ3RnWFZ2MURaZjhiRmRpaUtUMHZ4bXgiLCJtYWMiOiIwMGE5YTI2MDY1ZGIzMjg4MTVmYjdjZThhMzY2ZGM5OWE1Zjg5NjEzZGFhZWM5MzA4Y2I0ZDY0OTkxNzZmMGJiIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:06:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik5iNWVsQ1REWStYUmY0dGxrc09ZQ3c9PSIsInZhbHVlIjoiTDArdEQ4cWpmb1FGOTJCTkpzb2E5Vi9SVlRBOVMwQjlmdGlpWUJXb1RmQmhoTW1CcWNOdjlMN1Bwa2Y3TlVqWDIxNmFIM2xUSW53azVPSUxHdElCQ0hQYmV1MHRjb21WaGEwN2R2ZEN3Ky8wWjc0dUZ5VFlUR0p6aExaUlRVU2wiLCJtYWMiOiI0NzlhYWQ1ZThiZjFjZjRlN2U2ZTNmZDk2YTBhMTBmYjZkMWJkZjUwNDhiYWFmMGNlODUzYmI3ZTJlMTg3OGZhIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:06:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6Ik5sV0J6bmc3bmtEd3dvUE03QTZqRXc9PSIsInZhbHVlIjoiZ1B0NHFwRThLdVJNSlhHd0dLRFNtc2J5Vk9vQkNzUllxUUFVdkVlNWtuZXA2TE1FaVQ1VUtoYWdPZC9yOXJBRmxCZkNFQkxiR2hscHlreGpPeEo3cGgzWDU0NEUwNVJaWXQzTWVmSm1xZ3RnWFZ2MURaZjhiRmRpaUtUMHZ4bXgiLCJtYWMiOiIwMGE5YTI2MDY1ZGIzMjg4MTVmYjdjZThhMzY2ZGM5OWE1Zjg5NjEzZGFhZWM5MzA4Y2I0ZDY0OTkxNzZmMGJiIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:06:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627158299\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1354217969 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7Itr5Og7b7TMnK2UoYn0oaPyL7yUfGzPcudK4RTo</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Wwbr/NyKQwf8HzTJEQuk0.wHh4tXf59R4o2CCCZUFvzS4yG3ZU4Aa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n  \"<span class=sf-dump-key>Dashboard_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>businessCustomersOnly</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354217969\", {\"maxDepth\":0})</script>\n"}}
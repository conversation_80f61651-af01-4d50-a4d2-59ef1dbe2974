{"__meta": {"id": "X8022b3fa68d2e48828dcb90d8e6434c8", "datetime": "2025-06-21 17:45:23", "utime": **********.252947, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750527922.367705, "end": **********.25297, "duration": 0.8852648735046387, "duration_str": "885ms", "measures": [{"label": "Booting", "start": 1750527922.367705, "relative_start": 0, "end": 1750527922.690016, "relative_end": 1750527922.690016, "duration": 0.3223109245300293, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750527922.690029, "relative_start": 0.3223237991333008, "end": **********.252972, "relative_end": 1.9073486328125e-06, "duration": 0.5629429817199707, "duration_str": "563ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48625592, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 52, "nb_visible_statements": 52, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02793, "accumulated_duration_str": "27.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.095656, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 1.396}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.098403, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 1.396, "width_percent": 0.788}, {"sql": "select count(*) as aggregate from `users` where `team_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.106747, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:31", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=31", "ajax": false, "filename": "TeamStatsWidget.php", "line": "31"}, "connection": "edu_db2", "explain": null, "start_percent": 2.184, "width_percent": 1.289}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.1100302, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:32", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=32", "ajax": false, "filename": "TeamStatsWidget.php", "line": "32"}, "connection": "edu_db2", "explain": null, "start_percent": 3.473, "width_percent": 1.575}, {"sql": "select count(*) as aggregate from `shop_products` where `team_id` = 1 and `shop_products`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.1125882, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=33", "ajax": false, "filename": "TeamStatsWidget.php", "line": "33"}, "connection": "edu_db2", "explain": null, "start_percent": 5.048, "width_percent": 1.074}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.114777, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:34", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=34", "ajax": false, "filename": "TeamStatsWidget.php", "line": "34"}, "connection": "edu_db2", "explain": null, "start_percent": 6.122, "width_percent": 1.754}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` >= '2025-05-22 17:45:23' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-05-22 17:45:23", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.116957, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:39", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=39", "ajax": false, "filename": "TeamStatsWidget.php", "line": "39"}, "connection": "edu_db2", "explain": null, "start_percent": 7.877, "width_percent": 1.969}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `created_at` >= '2025-05-22 17:45:23' and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-05-22 17:45:23", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.119369, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "TeamStatsWidget.php:43", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TeamStatsWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\TeamStatsWidget.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FTeamStatsWidget.php&line=43", "ajax": false, "filename": "TeamStatsWidget.php", "line": "43"}, "connection": "edu_db2", "explain": null, "start_percent": 9.846, "width_percent": 1.862}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.129354, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 11.708, "width_percent": 1.146}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.132466, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 12.854, "width_percent": 2.363}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1350179, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 15.217, "width_percent": 2.148}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.137253, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 17.365, "width_percent": 2.435}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.139698, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 19.799, "width_percent": 2.077}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.142083, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 21.876, "width_percent": 2.649}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1447349, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 24.526, "width_percent": 2.077}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.147018, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 26.602, "width_percent": 3.366}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.149753, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 29.968, "width_percent": 2.363}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.152273, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 32.331, "width_percent": 2.184}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.154643, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 34.515, "width_percent": 2.506}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1571279, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 37.021, "width_percent": 2.685}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.159747, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RevenueChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\RevenueChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FRevenueChart.php&line=40", "ajax": false, "filename": "RevenueChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 39.706, "width_percent": 1.933}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.166545, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 41.64, "width_percent": 1.217}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.169161, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 42.857, "width_percent": 2.291}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1714401, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 45.149, "width_percent": 2.22}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.173795, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 47.368, "width_percent": 1.826}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1760862, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 49.194, "width_percent": 1.683}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.178216, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 50.877, "width_percent": 2.041}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.180492, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 52.918, "width_percent": 2.614}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.18291, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 55.532, "width_percent": 1.79}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1852071, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 57.322, "width_percent": 2.47}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1875372, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 59.792, "width_percent": 1.683}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1896179, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 61.475, "width_percent": 2.112}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.191897, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 63.588, "width_percent": 1.504}, {"sql": "select count(*) as aggregate from `shop_customers` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.194124, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/CustomersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\CustomersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FCustomersChart.php&line=40", "ajax": false, "filename": "CustomersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 65.091, "width_percent": 1.719}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.199156, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 66.81, "width_percent": 1.146}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '07' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "07", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.201766, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 67.956, "width_percent": 2.077}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '08' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "08", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2039719, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 70.032, "width_percent": 1.969}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '09' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "09", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.206137, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 72.001, "width_percent": 1.969}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '10' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.208633, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 73.971, "width_percent": 1.719}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '11' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "11", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.210723, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 75.689, "width_percent": 1.898}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2024 and month(`created_at`) = '12' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2024, "12", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.213025, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 77.587, "width_percent": 1.826}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '01' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "01", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.215347, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 79.413, "width_percent": 2.47}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '02' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "02", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.217892, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 81.883, "width_percent": 1.933}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '03' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "03", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2200239, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 83.817, "width_percent": 1.933}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '04' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "04", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.222281, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 85.75, "width_percent": 1.826}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '05' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "05", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2243972, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 87.576, "width_percent": 2.291}, {"sql": "select count(*) as aggregate from `shop_orders` where year(`created_at`) = 2025 and month(`created_at`) = '06' and `team_id` = 1 and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [2025, "06", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2267501, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "OrdersChart.php:40", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/OrdersChart.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\OrdersChart.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FOrdersChart.php&line=40", "ajax": false, "filename": "OrdersChart.php", "line": "40"}, "connection": "edu_db2", "explain": null, "start_percent": 89.868, "width_percent": 1.719}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.231711, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 91.586, "width_percent": 0.931}, {"sql": "select count(*) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` <= '2025-06-21 17:45:23' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:45:23", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 71}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.237303, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:71", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=71", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "71"}, "connection": "edu_db2", "explain": null, "start_percent": 92.517, "width_percent": 2.649}, {"sql": "select sum(`total_price`) as aggregate from `shop_orders` where `team_id` = 1 and `created_at` <= '2025-06-21 17:45:23' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:45:23", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 72}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.239832, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:72", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=72", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "72"}, "connection": "edu_db2", "explain": null, "start_percent": 95.166, "width_percent": 2.184}, {"sql": "select count(*) as aggregate from `shop_customers` where `team_id` = 1 and `created_at` <= '2025-06-21 17:45:23' and `shop_customers`.`deleted_at` is null and `shop_customers`.`team_id` = 1", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:45:23", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 73}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.242197, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:73", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=73", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "73"}, "connection": "edu_db2", "explain": null, "start_percent": 97.351, "width_percent": 1.898}, {"sql": "select count(*) as aggregate from `users` where `team_id` = 1 and `created_at` <= '2025-06-21 17:45:23'", "type": "query", "params": [], "bindings": [1, "2025-06-21 17:45:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 25}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.244178, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "StatsOverviewWidget.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverviewWidget.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Widgets\\StatsOverviewWidget.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FWidgets%2FStatsOverviewWidget.php&line=74", "ajax": false, "filename": "StatsOverviewWidget.php", "line": "74"}, "connection": "edu_db2", "explain": null, "start_percent": 99.248, "width_percent": 0.752}]}, "models": {"data": {"App\\Models\\Team": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.team-stats-widget #h75LmIf3Hxv4H3iguOUs": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.team-stats-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\TeamStatsWidget\"\n  \"id\" => \"h75LmIf3Hxv4H3iguOUs\"\n]", "app.filament.widgets.revenue-chart #apgYYCehyByJ0Wtl1UGl": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"97ab313d40eee230c600543f6d085ec0\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueChart\"\n  \"id\" => \"apgYYCehyByJ0Wtl1UGl\"\n]", "app.filament.widgets.customers-chart #R1m9YKlkG1Is0Lvss25q": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"95e447feb0484e3734e406fc9a599444\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.customers-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\CustomersChart\"\n  \"id\" => \"R1m9YKlkG1Is0Lvss25q\"\n]", "app.filament.widgets.orders-chart #SOrcWzVR95yNO5TbZ3re": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"fc55227bc58354bd4f71131d90bfd841\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.orders-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\OrdersChart\"\n  \"id\" => \"SOrcWzVR95yNO5TbZ3re\"\n]", "app.filament.widgets.stats-overview-widget #BIiBMr7x1kMekA1uBbYX": "array:4 [\n  \"data\" => array:1 [\n    \"filters\" => array:3 [\n      \"businessCustomersOnly\" => null\n      \"startDate\" => null\n      \"endDate\" => null\n    ]\n  ]\n  \"name\" => \"app.filament.widgets.stats-overview-widget\"\n  \"component\" => \"App\\Filament\\Widgets\\StatsOverviewWidget\"\n  \"id\" => \"BIiBMr7x1kMekA1uBbYX\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/default-team\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "8", "password_hash_web": "$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe", "Dashboard_filters": "array:3 [\n  \"businessCustomersOnly\" => null\n  \"startDate\" => null\n  \"endDate\" => null\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-334590235 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-334590235\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-878501082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-878501082\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2072443531 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"285 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;h75LmIf3Hxv4H3iguOUs&quot;,&quot;name&quot;:&quot;app.filament.widgets.team-stats-widget&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;de9b39f739ec896b2016fa3ed858267f50bab458f5451943db96779721f554fb&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;97ab313d40eee230c600543f6d085ec0&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;apgYYCehyByJ0Wtl1UGl&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c37f93e9791b5d6a84f83169ef8776c20dfe0c87fa1f3a1b5993648a84c1ef5b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;95e447feb0484e3734e406fc9a599444&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;R1m9YKlkG1Is0Lvss25q&quot;,&quot;name&quot;:&quot;app.filament.widgets.customers-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f4e42c26da54c42cc003ff8ca3daabed61e69622fb67ebdc36412c9ae3445130&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"343 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;fc55227bc58354bd4f71131d90bfd841&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;SOrcWzVR95yNO5TbZ3re&quot;,&quot;name&quot;:&quot;app.filament.widgets.orders-chart&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f9bbf84da9c7b6ef42c6b918aea51c0d57f391bf5898eeb2275666680600f186&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"395 characters\">{&quot;data&quot;:{&quot;filters&quot;:[{&quot;businessCustomersOnly&quot;:null,&quot;startDate&quot;:null,&quot;endDate&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;BIiBMr7x1kMekA1uBbYX&quot;,&quot;name&quot;:&quot;app.filament.widgets.stats-overview-widget&quot;,&quot;path&quot;:&quot;backend\\/default-team&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c61f1a74b5a49d1f17ccece65d4eeafcbd2f806b1804760062487976d15ad56e&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072443531\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1960653312 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2350</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,th;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imswb29jOWJiVTRUMk1DbHc2dzlkaHc9PSIsInZhbHVlIjoieEs1blJFUWJRMnhWaEVLcmVKdnRRQnNZcGlqdkx1dGNnMlVVSk9DSjlUalp6M1c5NFhSSWpLVS9ZcFE3VXhEdUhVQ3hkazByZXUzbmQ2bUdDZXorMUZ5aWlJTVp1NjhQaFliMU5NbmVsVC9WdENZRHU0NlpkT1VUaDRQY3IwaG5udnoxOTI4Y0xxc3NCTkRJbkQ5RWxrMGlSK2k0cjJ2dmMvWWZ1bDBWZC9OMzRQdU9CcEUxdVFnQ3ZNcTczK0RTZzBzT2lWaWduMHFUclQ5Wk1VS2UvdEVjL2NhRFIyRzRkN0o1Rmd2VzRKTT0iLCJtYWMiOiI5YjVmODhmOWU5YzA4M2E0YjMxNWE0NTlmNmNmZDg0YzZjMThmZDE5NzliOTA5NjI0Mjg2ODA0YmYwYTkwZWIzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlkvRnA1MlZ0MmNDbWdiY1V0UG10UlE9PSIsInZhbHVlIjoiS3BCN0p4bUU3S05sOGhxdll4bEs4aGRSbTY1MVV2U2J6bVZxZzdOUkRZRkQ4Y0RvSkpIVmpkVGhXS2hyNGEzVGFKR2hWcnowN3dpNy9FVnFub1A5Tlo1Visza3plQ1hxSTQzNjNuZEdENC9NYy9pc1ljK2IxV3FjVHRKaitRZ2wiLCJtYWMiOiI4ZDQyYmIyM2JiZjQ3MzFkOTdjMjg5NjEwM2FmZGI5MGUyZGEzNDRjMzY2M2MxMDFhYzgyYmI0NTVhOGU5NzA3IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6IktYQXBkUjFSSWVEZ0pJb1RuU2xjRWc9PSIsInZhbHVlIjoiYTNQUDNlTEFWTDZ4TC9XdW0yTG9oNUcvVTB4Rk1iWWhsRE1OY0J5TzQrd1lpdll1bDFGK055MFp0cXhMVFJ3c3ZBTm5oTnloMTJPMk1FZHh2SUN6NG5BU3BHSEJrQmZ6YjdnNWxKOVR3a01zVGt4OVpsbDBmRkpPR2NIN0VYdXAiLCJtYWMiOiI4OTdhOGU1ZTE0N2ZhYWYxOWE3YjIwNzUwZjMxMGIxZjQxZWI4NDFmOTBhYzRjNGZmZDMzYTE3ZWRmOWU2MDhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960653312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-999882586 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">8|uEm0mlZkc74gIicIsEcLEvV0115i1N3Y0ZTpeVcevaERH48GCO4oF8pIiIWH|$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bDakuFXZEjwkzwceMwuwBZu7yieBSY9wGZVonsnp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999882586\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-270416725 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:45:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFZa3h4Mlh4ekRwRy9PK2ttY1FvdUE9PSIsInZhbHVlIjoiMFhRcittMTdJK2I3WEk2OWI4aVZVaHh0WUZWT050czBPZVR6UFJYZjVzSkZieEJNa0Y5UnlzUjJQQllLUUhoMHdRNGhuNGI2YXRUaUw3Rmk2U0pSVEdhWVNudFBLVlRTem1iRlNzMjFUWXZEZk54MGFmbzZ1T0VHSElqbk95Um0iLCJtYWMiOiJjZmFjMmY2MmNiOTI0ZTY0ZmNjNmE1NWVlM2M3MGFjMTEwOWJmMDQzMzg4MGFlZDJhZTkzZWM3ZGI1NGJjOGIyIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:45:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6IlA3L0RVOW9lUklpWS9PTmdSVTVjUnc9PSIsInZhbHVlIjoiZWN5ZGo3eFdaSWNaUHc3U1plbTlKK2tLVmtWZHo4YUFTcmVyTGRHOU84NUtJRnVLR1B5c0FnUGIzNE8zMm1yaDRNWmZFMzhwanB5NFNSUXhhOGR6NDV6Z2orcnZNVFl4M1haa3MvWnZuS3VPeTVpWXVhblVWbmxaU08zZ0YveGsiLCJtYWMiOiJkN2M3MTdmNTM5MDNiZTIwZTA3NDRhZDAwNDI0ODVjMTY3NmZmNWFjNGM2ODhkMTExZjNlN2MwYzFjYWQwZjE2IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:45:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFZa3h4Mlh4ekRwRy9PK2ttY1FvdUE9PSIsInZhbHVlIjoiMFhRcittMTdJK2I3WEk2OWI4aVZVaHh0WUZWT050czBPZVR6UFJYZjVzSkZieEJNa0Y5UnlzUjJQQllLUUhoMHdRNGhuNGI2YXRUaUw3Rmk2U0pSVEdhWVNudFBLVlRTem1iRlNzMjFUWXZEZk54MGFmbzZ1T0VHSElqbk95Um0iLCJtYWMiOiJjZmFjMmY2MmNiOTI0ZTY0ZmNjNmE1NWVlM2M3MGFjMTEwOWJmMDQzMzg4MGFlZDJhZTkzZWM3ZGI1NGJjOGIyIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:45:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6IlA3L0RVOW9lUklpWS9PTmdSVTVjUnc9PSIsInZhbHVlIjoiZWN5ZGo3eFdaSWNaUHc3U1plbTlKK2tLVmtWZHo4YUFTcmVyTGRHOU84NUtJRnVLR1B5c0FnUGIzNE8zMm1yaDRNWmZFMzhwanB5NFNSUXhhOGR6NDV6Z2orcnZNVFl4M1haa3MvWnZuS3VPeTVpWXVhblVWbmxaU08zZ0YveGsiLCJtYWMiOiJkN2M3MTdmNTM5MDNiZTIwZTA3NDRhZDAwNDI0ODVjMTY3NmZmNWFjNGM2ODhkMTExZjNlN2MwYzFjYWQwZjE2IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:45:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270416725\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1025210861 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lNDOh3PWL1WMzXq14JHHAo80ymNrS33tphvDbe87</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/backend/default-team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$a4c4/q.MTBzrimzuIEibCusVTAYSVh1k0YpIlWL4No.hIw3Xv7ISe</span>\"\n  \"<span class=sf-dump-key>Dashboard_filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>businessCustomersOnly</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>startDate</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>endDate</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025210861\", {\"maxDepth\":0})</script>\n"}}
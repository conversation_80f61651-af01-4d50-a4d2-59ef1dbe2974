{"__meta": {"id": "X47794bf91c1868d71098d093bb78c00f", "datetime": "2025-06-21 17:55:27", "utime": **********.939017, "method": "GET", "uri": "/backend/default-team/tasks", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750528526.382088, "end": **********.939041, "duration": 1.556952953338623, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1750528526.382088, "relative_start": 0, "end": 1750528526.860464, "relative_end": 1750528526.860464, "duration": 0.4783761501312256, "duration_str": "478ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750528526.860474, "relative_start": 0.47838616371154785, "end": **********.939044, "relative_end": 3.0994415283203125e-06, "duration": 1.0785698890686035, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "route": {"uri": "GET backend/{tenant}/tasks", "domain": null, "middleware": "panel:backend, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\HandleSuperAdminAccess, Filament\\Http\\Middleware\\IdentifyTenant, App\\Http\\Middleware\\EnforceTenantAccess, BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant, App\\Http\\Middleware\\RestrictTenantPermissions", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable@__invoke", "as": "filament.backend.resources.tasks.index", "namespace": null, "prefix": "backend/{tenant:slug}/tasks", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01973, "accumulated_duration_str": "19.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2636788, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "edu_db2", "explain": null, "start_percent": 0, "width_percent": 3.041}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 2 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.271624, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "User.php:187", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=187", "ajax": false, "filename": "User.php", "line": "187"}, "connection": "edu_db2", "explain": null, "start_percent": 3.041, "width_percent": 2.686}, {"sql": "select * from `teams` where `slug` = 'default-team' limit 1", "type": "query", "params": [], "bindings": ["default-team"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleSuperAdminAccess.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.275197, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:189", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasTenancy.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=189", "ajax": false, "filename": "HasTenancy.php", "line": "189"}, "connection": "edu_db2", "explain": null, "start_percent": 5.727, "width_percent": 1.622}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3116179, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 7.349, "width_percent": 3.953}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.316653, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 11.303, "width_percent": 2.534}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.31923, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 13.837, "width_percent": 1.774}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.322145, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 15.611, "width_percent": 3.649}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.325456, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 19.26, "width_percent": 2.686}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3284879, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 21.946, "width_percent": 2.179}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.331488, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 24.126, "width_percent": 2.636}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.334758, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 26.761, "width_percent": 2.737}, {"sql": "select * from `tasks` where `team_id` = 1 and `start_datetime` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_datetime` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.345491, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 29.498, "width_percent": 4.511}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.348517, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 34.009, "width_percent": 1.774}, {"sql": "select * from `users` where `users`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 669}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3508968, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:529", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=529", "ajax": false, "filename": "TaskTimetable.php", "line": "529"}, "connection": "edu_db2", "explain": null, "start_percent": 35.783, "width_percent": 2.027}, {"sql": "select * from `teaching_schedules` where `team_id` = 1 and `start_time` between '2025-06-16 00:00:00' and '2025-06-22 23:59:59' order by `start_time` asc", "type": "query", "params": [], "bindings": [1, "2025-06-16 00:00:00", "2025-06-22 23:59:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 17, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.353436, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 37.81, "width_percent": 3.142}, {"sql": "select * from `users` where `users`.`id` in (1, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3565328, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 40.953, "width_percent": 3.041}, {"sql": "select * from `class_rooms` where `class_rooms`.`id` in (1, 2, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3591099, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 43.994, "width_percent": 1.825}, {"sql": "select * from `subjects` where `subjects`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.3614688, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 45.819, "width_percent": 1.267}, {"sql": "select * from `lessons` where `lessons`.`id` in (1, 3, 6, 7, 10, 13, 14, 16, 17, 19, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 670}, {"index": 22, "namespace": "view", "name": "filament.resources.task-resource.pages.task-timetable", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/filament/resources/task-resource/pages/task-timetable.blade.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.363549, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "TaskTimetable.php:557", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/TaskResource/Pages/TaskTimetable.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable.php", "line": 557}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTaskResource%2FPages%2FTaskTimetable.php&line=557", "ajax": false, "filename": "TaskTimetable.php", "line": "557"}, "connection": "edu_db2", "explain": null, "start_percent": 47.086, "width_percent": 2.078}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.78299, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 49.164, "width_percent": 2.331}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 1", "type": "query", "params": [], "bindings": ["new", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.788953, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 51.495, "width_percent": 6.842}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.794646, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 58.338, "width_percent": 3.497}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasRoutes.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 199}], "start": **********.823894, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 61.835, "width_percent": 3.193}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.8287861, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 65.028, "width_percent": 3.294}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.831738, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 68.322, "width_percent": 2.129}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.834689, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 70.451, "width_percent": 1.318}, {"sql": "select * from `teams` where `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php", "line": 509}, {"index": 17, "namespace": "view", "name": "filament-panels::components.tenant-menu", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views/vendor/filament-panels/components/tenant-menu.blade.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.84141, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "User.php:321", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\User.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=321", "ajax": false, "filename": "User.php", "line": "321"}, "connection": "edu_db2", "explain": null, "start_percent": 71.769, "width_percent": 2.99}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.847936, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 74.759, "width_percent": 2.129}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.850865, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 76.888, "width_percent": 1.875}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.854326, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 78.763, "width_percent": 2.179}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.859519, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 80.943, "width_percent": 2.99}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 2", "type": "query", "params": [], "bindings": ["new", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.862556, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 83.933, "width_percent": 1.673}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.865461, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 85.606, "width_percent": 1.521}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.870995, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 87.126, "width_percent": 2.23}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.873863, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 89.356, "width_percent": 2.078}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.877296, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 91.434, "width_percent": 2.484}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.881797, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:226", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\RoleResource.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=226", "ajax": false, "filename": "RoleResource.php", "line": "226"}, "connection": "edu_db2", "explain": null, "start_percent": 93.918, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `shop_orders` where `status` = 'new' and `shop_orders`.`deleted_at` is null and `shop_orders`.`team_id` = 3", "type": "query", "params": [], "bindings": ["new", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.884789, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "OrderResource.php:236", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Shop/OrderResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Shop\\OrderResource.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FShop%2FOrderResource.php&line=236", "ajax": false, "filename": "OrderResource.php", "line": "236"}, "connection": "edu_db2", "explain": null, "start_percent": 95.743, "width_percent": 2.331}, {"sql": "select count(*) as aggregate from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.887941, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "TeamResource.php:200", "source": {"index": 19, "namespace": null, "name": "app/Filament/Resources/TeamResource.php", "file": "C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\TeamResource.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FFilament%2FResources%2FTeamResource.php&line=200", "ajax": false, "filename": "TeamResource.php", "line": "200"}, "connection": "edu_db2", "explain": null, "start_percent": 98.074, "width_percent": 1.926}]}, "models": {"data": {"App\\Models\\TeachingSchedule": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeachingSchedule.php&line=1", "ajax": false, "filename": "TeachingSchedule.php", "line": "?"}}, "App\\Models\\Task": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTask.php&line=1", "ajax": false, "filename": "Task.php", "line": "?"}}, "App\\Models\\Lesson": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FLesson.php&line=1", "ajax": false, "filename": "Lesson.php", "line": "?"}}, "App\\Models\\User": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ClassRoom": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FClassRoom.php&line=1", "ajax": false, "filename": "ClassRoom.php", "line": "?"}}, "App\\Models\\Subject": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FSubject.php&line=1", "ajax": false, "filename": "Subject.php", "line": "?"}}, "App\\Models\\Team": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProjects%2FWeb%2Fhtdocs%2Fedu-v2%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 139, "is_counter": true}, "livewire": {"data": {"app.filament.resources.task-resource.pages.task-timetable #sp3GbNgnClulYtvvuql8": "array:4 [\n  \"data\" => array:20 [\n    \"selectedDate\" => \"2025-06-21\"\n    \"viewMode\" => \"week\"\n    \"editingTask\" => null\n    \"editingSchedule\" => null\n    \"editTaskData\" => []\n    \"editScheduleData\" => []\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.resources.task-resource.pages.task-timetable\"\n  \"component\" => \"App\\Filament\\Resources\\TaskResource\\Pages\\TaskTimetable\"\n  \"id\" => \"sp3GbNgnClulYtvvuql8\"\n]", "filament.livewire.global-search #3lJTlVAXlfaswINinjGF": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"3lJTlVAXlfaswINinjGF\"\n]", "filament.livewire.notifications #bjMhkVX5mThXUx4CAOXZ": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2913\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"bjMhkVX5mThXUx4CAOXZ\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 43, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2111493974 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111493974\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.28374, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1016665893 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016665893\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779523, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2030276890 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030276890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780039, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2144116645 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144116645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780559, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-744990318 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744990318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.793025, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-846959481 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846959481\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.793516, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1387310666 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387310666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.794088, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-560701492 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560701492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.797659, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1807442039 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807442039\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.827651, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-155396415 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155396415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.827901, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1020836010 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020836010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.828124, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1310290574 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310290574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.833864, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-120157400 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120157400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.834157, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1827493898 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827493898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.834371, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1703776063 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703776063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.836878, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-90145792 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-90145792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.846869, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1921204338 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921204338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.847097, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2016847001 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016847001\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.847308, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1045139275 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045139275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.853371, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1964909203 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964909203\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.853749, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-639537087 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639537087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.853987, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1255037662 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255037662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.856747, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1737141627 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737141627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858432, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1099887853 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099887853\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858675, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2138951105 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138951105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.85889, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-860326990 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860326990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.864644, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1444263524 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444263524\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.864936, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1720535219 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720535219\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865149, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1767389651 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767389651\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867402, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-393817127 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393817127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.86991, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-926780743 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926780743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870126, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-161617638 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161617638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870331, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-54306979 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54306979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876456, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-257218113 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257218113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876752, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-845566632 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845566632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876967, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1898034363 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898034363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879423, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Book,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Book]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1093075086 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Book</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Book</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Book]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093075086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880787, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ClassRoom,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\ClassRoom]\n]", "message_html": "<pre class=sf-dump id=sf-dump-520098528 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ClassRoom</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\ClassRoom</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\ClassRoom]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520098528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880997, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Course,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Course]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1500065599 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Course</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Course</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Course]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500065599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.881202, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Subject,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Subject]\n]", "message_html": "<pre class=sf-dump id=sf-dump-914344165 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Subject</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Subject</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Subject]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914344165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.887094, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Task,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\Task]\n]", "message_html": "<pre class=sf-dump id=sf-dump-195050638 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Task]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195050638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.887401, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\TeachingSchedule,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\TeachingSchedule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-654821279 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\TeachingSchedule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\TeachingSchedule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\TeachingSchedule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654821279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.887617, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 2,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-699187365 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-699187365\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.890262, "xdebug_link": null}]}, "session": {"_token": "HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "password_hash_web": "$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backend/default-team/tasks\"\n]"}, "request": {"path_info": "/backend/default-team/tasks", "status_code": "<pre class=sf-dump id=sf-dump-2001002084 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2001002084\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1355819938 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1355819938\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1197908301 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1197908301\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-54340835 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlhMVXJOM3UxdElLRmdvZEo0QWg1WVE9PSIsInZhbHVlIjoiNzVhV3pab29VMDZVRWducTY1aHQzbDV5UzZLQ2hWd1ZmWldtWjFlSWY0V0hkNEpuYTZjSVhVL0pCYmNUTngvZmRCb0pUOTZlM0thT1VScWdCbUxydU9oeUlWbnpVTS8yeHd4U2VzWVhOclczYUE4WEhMWGdCRHFqVEkxVWlkVklsdE9JL2t1bnRVQjlXMHF2N2pzTlRJbi9JVGJQdk42U1hBU0xIcnUzZVN1TnhDL1pmU01mTUsyZndTOGtMYjJpWG5qNkY4WnNkUFhuRDlEejRrL0l5QjNWZEpLc3lFdXA3Sm5hVEJPWHFRWT0iLCJtYWMiOiI1YWU3ODA0MTJjNGZhOTI5NzE0YTFkNmI3NzA2MDYwZWY4ZmUxMjI3ZDMwYjA1MDA3MWU5Y2NlMGI5OTFmMzRmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InRlZDZ0WkIzNDZzdkF1K3ljNXdMb0E9PSIsInZhbHVlIjoiUURZVDk1UEpoekZJOVhnQkVVamVvS1NXQ25rb2FjNkhQVTZUZlJKMzFBck5CVXlvVkJCSHh0YVljWC9vSG84ZkNqbGJOZjhVUnNEcVNQcDdiOXZ0OGdpM2x1aDI2U2dZMWtabWpJZVQ0dDNSUGptU1M4Tlo4TEQycUsvaGpIc3AiLCJtYWMiOiJiYTkzZGZiMDRlOGZhYzc3OGM3MDg3NjQ4NDg5OThmZjQyYzg0ZDA0OWQ0YmUwNWNjZjVlMzA3OWE0NjE3YWE2IiwidGFnIjoiIn0%3D; filament_demo_session=eyJpdiI6Ik9TcklFcTZBTmNyUlRJN0d4d0FIMkE9PSIsInZhbHVlIjoibVc4UmlXZ2tYTDlza0ROQzl0QTF5Zk0rdFB0cDY1bWlOa1d2b3Fib0I2NlB5STRaSXczQmdxS1JHaEFrdWc0dG5reThUOUltWUpyY0M4TE1XajVvQ2dHdzJ0YmtFWjEzdkNjb0J1eHN4dmExMWpFL2M1eml2cDBxdklCSzNzMDEiLCJtYWMiOiJiZWE3YzUwOWRiMzg1MjkyNDkzZmQwNWI2ZTY2YzE3MzJkZmJmOGI3NTc4Mjc0ZmU5NDRhNzQ5YzMyM2MxZTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54340835\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1899218442 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|ehyf0vWTi0nhH6BCaRuiRLr53ca7SdmqrBrbPg7dO3Vyvr6mhT3JSPnhkcqE|$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>filament_demo_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vsL2QWIgJub4i1B2dsBCWSF9PlW0AmpHCXRUUDpe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1899218442\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1398024822 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 17:55:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ino2TWxQamh0VFlWYitSL0RISjdibnc9PSIsInZhbHVlIjoiZGtlbW1lUEVsNjhLaTZYekVpVXV1SHNCTHAvbFdUeG5zOS9FcUhpWFEvQ25XM0lDMUlqRXo3NFdBbmgyLytXK1dTYStkbi9XMGs1M0NUbEVoV3FidFpDQ0ZDdWRPaE05c3dkOTFqMUxiY0VaVHcvb3hNQzdaelVHdElhdlBRVGUiLCJtYWMiOiIzNmEyZjU0NmNhZTg0NWUzZTQ3MjJmNjM1NDZkNTYwYTU4MDExOWRlZGQyNDIxZDM1Y2Q1OTI1MTUyZmNkOGVjIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:55:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">filament_demo_session=eyJpdiI6ImVGQ0NXTmNOZWVQcFpNdktiMkNHY0E9PSIsInZhbHVlIjoiT3N2QVhPemJqQWxoZFFaRm43UHNRL3hmMUtuaENVSVhFZXZqTUJjR2tMOFk4NXNwaXdkek5Eelh6OUNtQ0xJaWVTdU83N0xTMENjNERvRWNnWVZyQW9mVXgzNmF2ZWF3YzZuTHNndHVRZk1rM2NNell3K21JRlRON3J3eENHd2wiLCJtYWMiOiIxYzVhZmQ1NDJmMmU1ODA3ZmUwNjY5N2Y3NDEyMzQ1ZjJkMjM2OWQ5YWY5NzdiOWExYmY2NGEyZWJmMjJiZWViIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 19:55:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ino2TWxQamh0VFlWYitSL0RISjdibnc9PSIsInZhbHVlIjoiZGtlbW1lUEVsNjhLaTZYekVpVXV1SHNCTHAvbFdUeG5zOS9FcUhpWFEvQ25XM0lDMUlqRXo3NFdBbmgyLytXK1dTYStkbi9XMGs1M0NUbEVoV3FidFpDQ0ZDdWRPaE05c3dkOTFqMUxiY0VaVHcvb3hNQzdaelVHdElhdlBRVGUiLCJtYWMiOiIzNmEyZjU0NmNhZTg0NWUzZTQ3MjJmNjM1NDZkNTYwYTU4MDExOWRlZGQyNDIxZDM1Y2Q1OTI1MTUyZmNkOGVjIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:55:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">filament_demo_session=eyJpdiI6ImVGQ0NXTmNOZWVQcFpNdktiMkNHY0E9PSIsInZhbHVlIjoiT3N2QVhPemJqQWxoZFFaRm43UHNRL3hmMUtuaENVSVhFZXZqTUJjR2tMOFk4NXNwaXdkek5Eelh6OUNtQ0xJaWVTdU83N0xTMENjNERvRWNnWVZyQW9mVXgzNmF2ZWF3YzZuTHNndHVRZk1rM2NNell3K21JRlRON3J3eENHd2wiLCJtYWMiOiIxYzVhZmQ1NDJmMmU1ODA3ZmUwNjY5N2Y3NDEyMzQ1ZjJkMjM2OWQ5YWY5NzdiOWExYmY2NGEyZWJmMjJiZWViIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 19:55:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398024822\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1150820555 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmNSz8Bniz7C1SwrFKhpZt8QZVWGLMyD98v00CzC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$oMxG0V3oESMFZ1/Zy2DS3.4RRcIVd84ZjYrnrxEL98FTMIUZ6eX1a</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/backend/default-team/tasks</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150820555\", {\"maxDepth\":0})</script>\n"}}
[2025-06-22 04:55:06] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 2 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 2 at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\psy\\psysh\\src\\CodeCleaner.php(311): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php \\n = App\\\\M...', false)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\psy\\psysh\\src\\Shell.php(848): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\psy\\psysh\\src\\Shell.php(877): Psy\\Shell->addCode('\\n = App\\\\Models\\\\...', true)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\psy\\psysh\\src\\Shell.php(1342): Psy\\Shell->setCode('\\n = App\\\\Models\\\\...', true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('\\n = App\\\\Models\\\\...')
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\symfony\\console\\Application.php(1047): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\symfony\\console\\Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\symfony\\console\\Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-06-22 05:06:35] local.ERROR: Call to undefined method App\Models\Lesson::book() {"view":{"view":"C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\components\\select.blade.php","data":[]},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Call to undefined method App\\Models\\Lesson::book() at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('book')
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2367): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'book', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(880): Illuminate\\Database\\Eloquent\\Model->__call('book', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(117): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\Concerns\\{closure}()
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(879): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(40): Illuminate\\Database\\Eloquent\\Builder->getRelationWithoutConstraints('book')
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(153): Illuminate\\Database\\Eloquent\\Builder->has('book', '>=', 1, 'and', Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\AssignmentResource.php(81): Illuminate\\Database\\Eloquent\\Builder->whereHas('book', Object(Closure))
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\AssignmentResource::App\\Filament\\Resources\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(772): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Forms\\Components\\Select::Filament\\Forms\\Components\\{closure}(Object(Filament\\Forms\\Components\\Select))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php(32): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(652): Filament\\Forms\\Components\\Select->getOptions()
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\components\\select.blade.php(145): Filament\\Forms\\Components\\Select->getOptionsForJs()
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\component-container.blade.php(101): e(Object(Filament\\Forms\\Components\\Select))
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\components\\section.blade.php(29): e(Object(Filament\\Forms\\ComponentContainer))
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\component-container.blade.php(101): e(Object(Filament\\Forms\\Components\\Section))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views\\vendor\\filament-panels\\resources\\pages\\edit-record.blade.php(16): e(Object(Filament\\Forms\\Form))
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views\\vendor\\filament-panels\\resources\\pages\\edit-record.blade.php(33): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->{closure}()
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), Object(Closure))
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '<div></div>')
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#67 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#68 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#69 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#70 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('default-team', '2')
#71 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '__invoke')
#72 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#73 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#74 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\RestrictTenantPermissions.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictTenantPermissions->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\bezhansalleh\\filament-shield\\src\\Middleware\\SyncShieldTenant.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\EnforceTenantAccess.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnforceTenantAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\IdentifyTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HandleSuperAdminAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#101 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#102 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#107 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#108 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#109 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#110 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#111 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#112 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#113 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#114 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#115 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#119 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#120 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#121 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#122 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#123 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#124 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#125 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#126 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#127 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#128 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#129 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#130 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#131 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#132 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#133 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#134 C:\\Projects\\Web\\htdocs\\edu-v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#135 C:\\Projects\\Web\\htdocs\\edu-v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#136 {main}

[previous exception] [object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Lesson::book() at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('book')
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2367): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'book', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(880): Illuminate\\Database\\Eloquent\\Model->__call('book', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(117): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\Concerns\\{closure}()
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(879): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(40): Illuminate\\Database\\Eloquent\\Builder->getRelationWithoutConstraints('book')
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(153): Illuminate\\Database\\Eloquent\\Builder->has('book', '>=', 1, 'and', Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\AssignmentResource.php(81): Illuminate\\Database\\Eloquent\\Builder->whereHas('book', Object(Closure))
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\AssignmentResource::App\\Filament\\Resources\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(772): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Forms\\Components\\Select::Filament\\Forms\\Components\\{closure}(Object(Filament\\Forms\\Components\\Select))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php(32): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(652): Filament\\Forms\\Components\\Select->getOptions()
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\6a7488e0bb64ef0f8226aa7c0f343869.php(168): Filament\\Forms\\Components\\Select->getOptionsForJs()
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\02ed9fcbe0d4f410a7167cd631d68a1d.php(140): e(Object(Filament\\Forms\\Components\\Select))
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\7a06339d13317e48a33077c8537d0aba.php(29): e(Object(Filament\\Forms\\ComponentContainer))
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\02ed9fcbe0d4f410a7167cd631d68a1d.php(140): e(Object(Filament\\Forms\\Components\\Section))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\07d8dcf2bb53eeb4c1fb3b0a5b00f384.php(35): e(Object(Filament\\Forms\\Form))
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\07d8dcf2bb53eeb4c1fb3b0a5b00f384.php(79): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->{closure}()
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), Object(Closure))
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '<div></div>')
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#67 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#68 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#69 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#70 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('default-team', '2')
#71 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '__invoke')
#72 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#73 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#74 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\RestrictTenantPermissions.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictTenantPermissions->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\bezhansalleh\\filament-shield\\src\\Middleware\\SyncShieldTenant.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\EnforceTenantAccess.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnforceTenantAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\IdentifyTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HandleSuperAdminAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#101 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#102 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#107 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#108 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#109 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#110 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#111 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#112 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#113 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#114 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#115 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#119 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#120 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#121 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#122 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#123 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#124 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#125 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#126 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#127 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#128 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#129 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#130 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#131 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#132 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#133 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#134 C:\\Projects\\Web\\htdocs\\edu-v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#135 C:\\Projects\\Web\\htdocs\\edu-v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#136 {main}
"} 
[2025-06-22 05:12:37] local.ERROR: Trait "App\Models\HasTeamScopedPolymorphicRelations" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"App\\Models\\HasTeamScopedPolymorphicRelations\" not found at C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\Lesson.php:11)
[stacktrace]
#0 {main}
"} 
[2025-06-22 05:14:22] local.ERROR: Trait "App\Models\HasTeamScopedPolymorphicRelations" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"App\\Models\\HasTeamScopedPolymorphicRelations\" not found at C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\Book.php:12)
[stacktrace]
#0 {main}
"} 
[2025-06-22 05:14:54] local.ERROR: Trait "App\Models\HasTeamScopedPolymorphicRelations" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"App\\Models\\HasTeamScopedPolymorphicRelations\" not found at C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Models\\Course.php:12)
[stacktrace]
#0 {main}
"} 
[2025-06-22 05:37:23] local.ERROR: Call to a member function getRelationExistenceQuery() on null {"view":{"view":"C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\components\\select.blade.php","data":[]},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Call to a member function getRelationExistenceQuery() on null at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php:54)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(153): Illuminate\\Database\\Eloquent\\Builder->has(NULL, '>=', 1, 'and', Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\AssignmentResource.php(81): Illuminate\\Database\\Eloquent\\Builder->whereHas('book', Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\AssignmentResource::App\\Filament\\Resources\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(772): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Forms\\Components\\Select::Filament\\Forms\\Components\\{closure}(Object(Filament\\Forms\\Components\\Select))
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php(32): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(652): Filament\\Forms\\Components\\Select->getOptions()
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\components\\select.blade.php(145): Filament\\Forms\\Components\\Select->getOptionsForJs()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\component-container.blade.php(101): e(Object(Filament\\Forms\\Components\\Select))
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\components\\section.blade.php(29): e(Object(Filament\\Forms\\ComponentContainer))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\resources\\views\\component-container.blade.php(101): e(Object(Filament\\Forms\\Components\\Section))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views\\vendor\\filament-panels\\resources\\pages\\edit-record.blade.php(16): e(Object(Filament\\Forms\\Form))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\resources\\views\\vendor\\filament-panels\\resources\\pages\\edit-record.blade.php(33): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->{closure}()
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '<div></div>')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('default-team', '2')
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '__invoke')
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#67 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#68 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\RestrictTenantPermissions.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictTenantPermissions->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\bezhansalleh\\filament-shield\\src\\Middleware\\SyncShieldTenant.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\EnforceTenantAccess.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnforceTenantAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\IdentifyTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HandleSuperAdminAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#95 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#96 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#97 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#98 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#99 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#100 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#101 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#102 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#104 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#105 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#106 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#107 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#108 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#109 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#116 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#117 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#118 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#119 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#120 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#121 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#122 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#123 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#124 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#125 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#126 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#127 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#128 C:\\Projects\\Web\\htdocs\\edu-v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#129 C:\\Projects\\Web\\htdocs\\edu-v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#130 {main}

[previous exception] [object] (Error(code: 0): Call to a member function getRelationExistenceQuery() on null at C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php:54)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(153): Illuminate\\Database\\Eloquent\\Builder->has(NULL, '>=', 1, 'and', Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\AssignmentResource.php(81): Illuminate\\Database\\Eloquent\\Builder->whereHas('book', Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\AssignmentResource::App\\Filament\\Resources\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(772): Filament\\Support\\Components\\Component->evaluate(Object(Closure), Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Forms\\Components\\Select::Filament\\Forms\\Components\\{closure}(Object(Filament\\Forms\\Components\\Select))
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php(32): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\forms\\src\\Components\\Select.php(652): Filament\\Forms\\Components\\Select->getOptions()
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\6a7488e0bb64ef0f8226aa7c0f343869.php(168): Filament\\Forms\\Components\\Select->getOptionsForJs()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\02ed9fcbe0d4f410a7167cd631d68a1d.php(140): e(Object(Filament\\Forms\\Components\\Select))
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\7a06339d13317e48a33077c8537d0aba.php(29): e(Object(Filament\\Forms\\ComponentContainer))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\02ed9fcbe0d4f410a7167cd631d68a1d.php(140): e(Object(Filament\\Forms\\Components\\Section))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\support\\src\\Components\\ViewComponent.php(106): Illuminate\\View\\View->render()
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(128): Filament\\Support\\Components\\ViewComponent->toHtml()
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\07d8dcf2bb53eeb4c1fb3b0a5b00f384.php(35): e(Object(Filament\\Forms\\Form))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\storage\\framework\\views\\07d8dcf2bb53eeb4c1fb3b0a5b00f384.php(79): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->{closure}()
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Projects\\\\Web...')
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), Object(Closure))
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '<div></div>')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('default-team', '2')
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\AssignmentResource\\Pages\\EditAssignment), '__invoke')
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#67 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#68 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\RestrictTenantPermissions.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictTenantPermissions->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\bezhansalleh\\filament-shield\\src\\Middleware\\SyncShieldTenant.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): BezhanSalleh\\FilamentShield\\Middleware\\SyncShieldTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\EnforceTenantAccess.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnforceTenantAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\IdentifyTenant.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\IdentifyTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Http\\Middleware\\HandleSuperAdminAccess.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\HandleSuperAdminAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#95 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#96 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#97 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#98 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#99 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#100 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#101 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#102 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#104 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#105 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#106 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#107 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#108 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#109 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#116 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#117 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#118 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#119 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#120 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#121 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#122 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#123 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#124 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#125 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#126 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#127 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#128 C:\\Projects\\Web\\htdocs\\edu-v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#129 C:\\Projects\\Web\\htdocs\\edu-v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#130 {main}
"} 

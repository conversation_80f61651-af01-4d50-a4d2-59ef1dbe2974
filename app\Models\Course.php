<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasTeamScopedPolymorphicRelations;

class Course extends Model
{
    use HasFactory;
    use HasTeamScopedPolymorphicRelations;

    protected $fillable = [
        'team_id',
        'subject_id',
        'title',
        'description',
        'type',
        'price',
        'currency',
        'duration_hours',
        'difficulty_level',
        'max_students',
        'start_date',
        'end_date',
        'schedule',
        'instructor',
        'requirements',
        'objectives',
        'certificate_template',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'price' => 'decimal:2',
        'duration_hours' => 'integer',
        'max_students' => 'integer',
        'sort_order' => 'integer',
        'start_date' => 'date',
        'end_date' => 'date',
        'schedule' => 'array',
    ];

    /**
     * Get the team that owns the course
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the subject that the course belongs to
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the lessons for this course
     */
    public function lessons()
    {
        return $this->belongsToMany(Lesson::class, 'lessonables', 'lessonable_id', 'lesson_id')
            ->wherePivot('lessonable_type', static::class)
            ->withPivot(['team_id', 'sort_order', 'lessonable_type'])
            ->withTimestamps()
            ->orderByPivot('sort_order');
    }

    /**
     * Scope a query to only include active courses
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured courses
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to filter by type
     */
    public function scopeType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Get the formatted price with currency
     */
    public function getFormattedPriceAttribute(): string
    {
        if (!$this->price) {
            return 'Free';
        }

        return $this->currency . ' ' . number_format($this->price, 2);
    }

    /**
     * Get the course duration in a readable format
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_hours) {
            return 'Duration not set';
        }

        if ($this->duration_hours < 1) {
            return ($this->duration_hours * 60) . ' minutes';
        }

        return $this->duration_hours . ' hour' . ($this->duration_hours > 1 ? 's' : '');
    }

    /**
     * Get the lesson count
     */
    public function getLessonCountAttribute(): int
    {
        return $this->lessons()->count();
    }

    /**
     * Get the course status based on dates
     */
    public function getStatusAttribute(): string
    {
        if (!$this->start_date || !$this->end_date) {
            return 'scheduled';
        }

        $now = now()->toDateString();

        if ($now < $this->start_date->toDateString()) {
            return 'upcoming';
        } elseif ($now > $this->end_date->toDateString()) {
            return 'completed';
        } else {
            return 'ongoing';
        }
    }

    /**
     * Check if course is free
     */
    public function isFree(): bool
    {
        return !$this->price || $this->price <= 0;
    }

    /**
     * Check if course has started
     */
    public function hasStarted(): bool
    {
        return $this->start_date && now()->toDateString() >= $this->start_date->toDateString();
    }

    /**
     * Check if course has ended
     */
    public function hasEnded(): bool
    {
        return $this->end_date && now()->toDateString() > $this->end_date->toDateString();
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            // Add polymorphic columns
            $table->morphs('lessonable'); // adds lessonable_id and lessonable_type
            $table->index(['lessonable_type', 'lessonable_id', 'sort_order']);
        });

        // Migrate data from lessonables table back to lessons table
        $lessonables = DB::table('lessonables')->get();
        
        foreach ($lessonables as $lessonable) {
            DB::table('lessons')
                ->where('id', $lessonable->lesson_id)
                ->update([
                    'lessonable_id' => $lessonable->lessonable_id,
                    'lessonable_type' => $lessonable->lessonable_type,
                ]);
        }

        echo "Migrated " . count($lessonables) . " lesson relationships to polymorphic columns.\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore data to lessonables table
        $lessons = DB::table('lessons')
            ->whereNotNull('lessonable_id')
            ->whereNotNull('lessonable_type')
            ->get();

        foreach ($lessons as $lesson) {
            // Get team_id from the related model
            $teamId = null;
            if ($lesson->lessonable_type === 'App\\Models\\Book') {
                $book = DB::table('books')->where('id', $lesson->lessonable_id)->first();
                $teamId = $book ? $book->team_id : null;
            } elseif ($lesson->lessonable_type === 'App\\Models\\Course') {
                $course = DB::table('courses')->where('id', $lesson->lessonable_id)->first();
                $teamId = $course ? $course->team_id : null;
            }

            DB::table('lessonables')->insert([
                'lesson_id' => $lesson->id,
                'lessonable_id' => $lesson->lessonable_id,
                'lessonable_type' => $lesson->lessonable_type,
                'team_id' => $teamId,
                'sort_order' => $lesson->sort_order ?? 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        Schema::table('lessons', function (Blueprint $table) {
            $table->dropMorphs('lessonable');
        });

        echo "Restored " . count($lessons) . " lesson relationships to lessonables table.\n";
    }
};

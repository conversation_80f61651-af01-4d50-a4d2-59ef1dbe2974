<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('lessonables');
        echo "Dropped lessonables table as it's no longer needed.\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('lessonables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lesson_id')->constrained('lessons')->cascadeOnDelete();
            $table->morphs('lessonable'); // lessonable_id and lessonable_type
            $table->foreignId('team_id')->nullable()->constrained('teams')->nullOnDelete();
            $table->integer('sort_order')->default(0); // Order of lesson within the course/book
            $table->timestamps();

            // Indexes for performance
            $table->index(['lesson_id', 'lessonable_type', 'lessonable_id']);
            $table->index(['lessonable_type', 'lessonable_id', 'sort_order']);
            $table->index(['team_id', 'lessonable_type']);
            
            // Unique constraint to prevent duplicate lesson assignments
            $table->unique(['lesson_id', 'lessonable_id', 'lessonable_type'], 'unique_lesson_assignment');
        });
    }
};

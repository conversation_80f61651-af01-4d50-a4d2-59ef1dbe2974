<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AssignmentPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Assignment permissions
        $assignmentPermissions = [
            'view_any_assignment',
            'view_assignment',
            'create_assignment',
            'update_assignment',
            'delete_assignment',
            'delete_any_assignment',
            'replicate_assignment',
            'reorder_assignment',
        ];

        // Create AssignmentSubmission permissions
        $submissionPermissions = [
            'view_any_assignment::submission',
            'view_assignment::submission',
            'create_assignment::submission',
            'update_assignment::submission',
            'delete_assignment::submission',
            'delete_any_assignment::submission',
            'replicate_assignment::submission',
            'reorder_assignment::submission',
        ];

        // Combine all permissions
        $allPermissions = array_merge($assignmentPermissions, $submissionPermissions);

        // Create permissions if they don't exist
        foreach ($allPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        $this->command->info('Assignment permissions created successfully.');

        // Assign permissions to roles
        $this->assignAssignmentPermissionsToRoles();

        $this->command->info('Assignment permissions assigned to roles successfully.');
    }

    private function assignAssignmentPermissionsToRoles()
    {
        // Super Admin - All permissions (already has all permissions)
        $superAdmin = Role::where('name', 'super_admin')->first();
        if ($superAdmin) {
            $superAdmin->givePermissionTo(Permission::all());
        }

        // School Admin (team_admin) - Full management
        $teamAdmin = Role::where('name', 'team_admin')->first();
        if ($teamAdmin) {
            $teamAdmin->givePermissionTo([
                // Assignment permissions
                'view_any_assignment',
                'view_assignment',
                'create_assignment',
                'update_assignment',
                'delete_assignment',
                'delete_any_assignment',
                'replicate_assignment',
                'reorder_assignment',

                // Submission permissions
                'view_any_assignment::submission',
                'view_assignment::submission',
                'create_assignment::submission',
                'update_assignment::submission',
                'delete_assignment::submission',
                'delete_any_assignment::submission',
                'replicate_assignment::submission',
                'reorder_assignment::submission',
            ]);
        }

        // School role (from RoleSeeder) - Full management
        $school = Role::where('name', 'school')->first();
        if ($school) {
            $school->givePermissionTo([
                // Assignment permissions
                'view_any_assignment',
                'view_assignment',
                'create_assignment',
                'update_assignment',
                'delete_assignment',
                'delete_any_assignment',
                'replicate_assignment',
                'reorder_assignment',

                // Submission permissions
                'view_any_assignment::submission',
                'view_assignment::submission',
                'create_assignment::submission',
                'update_assignment::submission',
                'delete_assignment::submission',
                'delete_any_assignment::submission',
                'replicate_assignment::submission',
                'reorder_assignment::submission',
            ]);
        }

        // Teacher - Full assignment creation and submission management
        $teacher = Role::where('name', 'teacher')->first();
        if ($teacher) {
            $teacher->givePermissionTo([
                // Assignment permissions - teachers can manage assignments
                'view_any_assignment',
                'view_assignment',
                'create_assignment',
                'update_assignment',
                'replicate_assignment',

                // Submission permissions - teachers can fully manage submissions (grading)
                'view_any_assignment::submission',
                'view_assignment::submission',
                'create_assignment::submission',
                'update_assignment::submission',
                'replicate_assignment::submission',
                'reorder_assignment::submission',
            ]);
        }

        // Team Member - Read-only access
        $teamMember = Role::where('name', 'team_member')->first();
        if ($teamMember) {
            $teamMember->givePermissionTo([
                'view_any_assignment',
                'view_assignment',
                'view_any_assignment::submission',
                'view_assignment::submission',
            ]);
        }

        // Parent - Read-only access to see assignments and their child's submissions
        $parent = Role::where('name', 'parent')->first();
        if ($parent) {
            $parent->givePermissionTo([
                'view_any_assignment',
                'view_assignment',
                'view_any_assignment::submission',
                'view_assignment::submission',
            ]);
        }

        // Student - Can view assignments and create/update their own submissions
        $student = Role::where('name', 'student')->first();
        if ($student) {
            $student->givePermissionTo([
                'view_any_assignment',
                'view_assignment',
                'view_assignment::submission', // Can view their own submissions
                'create_assignment::submission', // Can submit assignments
                'update_assignment::submission', // Can update their own submissions (before deadline)
            ]);
        }
    }
}
